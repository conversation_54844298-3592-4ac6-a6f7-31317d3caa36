# 广东省智慧教育平台OCR识别系统

## 功能概述

本系统用于处理Excel文件中的PDF下载链接，通过OCR技术自动识别PDF文档中的人员信息，包括：
- 姓名（案例教师姓名）
- 手机号码
- 身份证号

## 系统架构

```
gdae-ocr/
├── ocr/
│   ├── __init__.py
│   └── 2025_zxxzhjy.py    # 主要处理模块
├── config.py              # 配置文件
├── run_ocr.py             # 启动脚本
├── test_ocr.py            # 测试脚本
├── example.py             # 使用示例
└── README.md              # 说明文档
```

## 依赖要求

### Python库依赖
- `pandas` - Excel文件处理
- `openpyxl` - Excel文件读写
- `requests` - HTTP请求
- `pypdfium2` - PDF处理和渲染
- `Pillow` - 图像处理
- `base64` - 图像编码

### 外部服务依赖
- OCR API服务：`http://127.0.0.1:1224/api/ocr`

## 安装和配置

### 1. 安装依赖
```bash
# 项目已包含所需依赖，如需单独安装：
pip install pandas openpyxl requests pypdfium2 Pillow
```

### 2. 启动OCR服务
确保OCR服务运行在 `http://127.0.0.1:1224/api/ocr`

### 3. 准备Excel文件
Excel文件需要包含一列名为 `tjb_url` 的PDF下载链接

### 4. 配置系统（可选）
系统支持通过环境变量或配置文件进行自定义：

#### 环境变量配置
```bash
export OCR_API_URL="http://127.0.0.1:1224/api/ocr"
export OCR_TIMEOUT="30"
export PDF_DOWNLOAD_TIMEOUT="60"
export PDF_RENDER_DPI="300"
export LOG_LEVEL="INFO"
export LOG_FILE="ocr_processing.log"
```

#### 配置文件
编辑 `config.py` 文件修改默认配置：
- `OCR_API_URL`: OCR服务地址
- `OCR_TIMEOUT`: OCR请求超时时间（秒）
- `PDF_DOWNLOAD_TIMEOUT`: PDF下载超时时间（秒）
- `PDF_RENDER_DPI`: PDF渲染DPI（影响OCR质量）
- `EXTRACT_PATTERNS`: 信息提取正则表达式
- `EXCEL_URL_COLUMN_NAMES`: Excel中URL列的候选名称
- `USE_STRUCTURED_OCR`: 是否使用结构化OCR数据
- `OCR_CONFIDENCE_THRESHOLD`: OCR置信度阈值
- `SPATIAL_MATCHING_ENABLED`: 是否启用空间位置匹配

## 使用方法

### 快速开始
```bash
# 进入项目目录
cd gdae-ocr

# 使用启动脚本（推荐）
python run_ocr.py your_excel_file.xlsx

# 或者交互模式
python run_ocr.py

# 运行测试
python run_ocr.py --test
```

### 命令行使用
```bash
# 直接运行主程序
python ocr/2025_zxxzhjy.py

# 或者使用示例脚本
python example.py
```

### 程序化使用
```python
import importlib.util
from pathlib import Path

# 动态加载模块
module_path = Path("ocr/2025_zxxzhjy.py")
spec = importlib.util.spec_from_file_location("zxxzhjy_module", module_path)
ocr_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(ocr_module)

# 创建处理器
processor = ocr_module.ExcelProcessor('input.xlsx')

# 处理Excel文件
result_df = processor.process_excel()

# 保存结果
processor.save_results(result_df, 'output.xlsx')
```

### 配置自定义
```python
# 使用自定义OCR API地址
processor = ocr_module.OCRProcessor(ocr_api_url="http://your-api-server:8080/api/ocr")

# 或者通过环境变量配置
import os
os.environ['OCR_API_URL'] = 'http://your-api-server:8080/api/ocr'
os.environ['PDF_DOWNLOAD_TIMEOUT'] = '120'
os.environ['LOG_LEVEL'] = 'DEBUG'
```

## 输入格式

### Excel文件要求
- 必须包含 `tjb_url` 列（或其他包含URL的列）
- URL应指向可访问的PDF文件
- 支持 `.xlsx` 和 `.xls` 格式

### PDF文件要求
- 包含表格形式的人员信息
- 信息标识符：
  - `案例教师姓名` - 后跟姓名
  - `手机号码` - 后跟11位手机号
  - `身份证号` - 后跟15位或18位身份证号

## 输出格式

### 结果Excel文件
包含以下列：
- `original_row` - 原始行号
- `pdf_url` - PDF文件URL
- `processing_status` - 处理状态（success/failed/skipped）
- `error_message` - 错误信息
- `person_count` - 提取的人员数量
- `person_index` - 人员序号
- `name` - 姓名
- `phone` - 手机号
- `id_card` - 身份证号

### 统计报告
生成 `*_stats.txt` 文件，包含：
- 总处理行数
- 成功/失败/跳过统计
- 提取人员总数
- 成功率

## 核心功能模块

### OCRProcessor类
- `download_pdf()` - 下载PDF文件
- `pdf_to_images()` - PDF转图片
- `call_ocr_api()` - 调用OCR API（支持结构化数据）
- `extract_person_info()` - 增强的人员信息提取
- `process_pdf_url()` - 处理单个PDF URL

### 增强的信息提取功能
系统采用多策略匹配机制，显著提高表格形式PDF的信息提取准确性：

#### 策略1: 基于坐标的空间位置匹配（优先）
- 利用OCR返回的文本框坐标信息
- 识别表格结构和单元格关系
- 支持标识文本与数据的空间关联匹配
- 处理左右相邻、上下相邻的单元格

#### 策略2: 改进的正则表达式跨行匹配（备选）
- 支持标识文本与数据跨行显示的情况
- 智能处理身份证号码分段显示
- 改进的文本清理和重组算法
- 支持表格边框字符过滤

#### 策略3: 传统正则表达式匹配（兜底）
- 保持向后兼容性
- 处理简单格式的文本
- 基于位置的信息配对

### 数据质量控制
- OCR置信度过滤（默认阈值0.5）
- 多重数据验证机制
- 智能文本清理和标准化
- 提取结果交叉验证

### ExcelProcessor类
- `load_excel()` - 加载Excel文件
- `find_url_column()` - 查找URL列
- `process_excel()` - 处理整个Excel文件
- `save_results()` - 保存处理结果

## 错误处理

系统包含完善的错误处理机制：
- 网络请求超时和重试
- PDF文件格式验证
- OCR API调用异常处理
- 数据验证和清洗
- 详细的日志记录

## 性能优化

- 使用会话复用减少连接开销
- 内存优化的PDF处理
- 批量处理和进度跟踪
- 基于身份证号的去重机制

## 测试

运行测试脚本：
```bash
# 基础功能测试
python test_ocr.py

# 增强信息提取功能测试
python test_enhanced_extraction.py
```

测试内容：
- OCR API连接测试
- 图像处理测试
- Excel文件处理测试
- 基础信息提取验证
- 结构化数据提取测试
- 跨行文本处理测试
- 分段身份证号提取测试
- 向后兼容性验证

## 日志

系统会生成详细的日志文件 `ocr_processing.log`，包含：
- 处理进度信息
- 错误和警告信息
- 性能统计数据

## 注意事项

1. **OCR服务依赖**：确保OCR服务正常运行
2. **网络连接**：需要稳定的网络连接下载PDF文件
3. **内存使用**：大量PDF处理可能占用较多内存
4. **处理时间**：根据PDF数量和大小，处理时间可能较长
5. **数据验证**：系统会验证提取数据的格式，无效数据会被过滤

## 故障排除

### 常见问题
1. **OCR API连接失败**
   - 检查服务是否运行
   - 验证API地址和端口

2. **PDF下载失败**
   - 检查网络连接
   - 验证PDF URL有效性

3. **信息提取不准确**
   - 检查PDF格式是否符合要求
   - 调整OCR参数设置

4. **Excel文件处理失败**
   - 检查文件格式和权限
   - 验证列名是否正确

## 扩展功能

系统设计支持以下扩展：
- 支持更多PDF格式
- 添加更多信息提取规则
- 集成其他OCR服务
- 支持批量并行处理
- 添加Web界面

## 联系支持

如有问题或建议，请联系开发团队。

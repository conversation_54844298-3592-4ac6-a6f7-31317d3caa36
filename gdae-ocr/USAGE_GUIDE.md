# OCR系统使用指南

## 快速开始

### 1. 系统检查
```bash
# 检查所有依赖和配置
python check_dependencies.py
```

### 2. 准备Excel文件
创建包含PDF链接的Excel文件，确保有一列名为 `tjb_url`：

| tjb_url | title | description |
|---------|-------|-------------|
| https://example.com/doc1.pdf | 案例1 | 描述1 |
| https://example.com/doc2.pdf | 案例2 | 描述2 |

### 3. 运行处理
```bash
# 方式1: 使用启动脚本（推荐）
python run_ocr.py your_file.xlsx

# 方式2: 交互模式
python run_ocr.py

# 方式3: 直接运行主程序
python ocr/2025_zxxzhjy.py
```

### 4. 查看结果
处理完成后会生成：
- `your_file_ocr_results.xlsx` - 详细结果
- `your_file_ocr_results_stats.txt` - 统计报告
- `ocr_processing.log` - 处理日志

## 详细使用说明

### 配置OCR服务
确保OCR服务运行在正确的地址：
```bash
# 默认地址
http://127.0.0.1:1224/api/ocr

# 如需修改，设置环境变量
export OCR_API_URL="http://your-server:port/api/ocr"
```

### Excel文件要求
- 支持 `.xlsx` 和 `.xls` 格式
- 必须包含PDF链接列，支持的列名：
  - `tjb_url` (优先)
  - `url`
  - `pdf_url`
  - `download_url`

### PDF文件要求
PDF应包含表格形式的人员信息，包含以下标识：
- `案例教师姓名` - 后跟姓名
- `手机号码` - 后跟11位手机号
- `身份证号` - 后跟15位或18位身份证号

### 结果文件说明
输出Excel包含以下列：
- `original_row` - 原始Excel行号
- `pdf_url` - PDF文件URL
- `processing_status` - 处理状态
- `error_message` - 错误信息
- `person_count` - 提取的人员数量
- `person_index` - 人员序号
- `name` - 姓名
- `phone` - 手机号
- `id_card` - 身份证号

## 高级使用

### 自定义配置
编辑 `config.py` 或设置环境变量：
```python
# OCR API配置
OCR_API_URL = "http://127.0.0.1:1224/api/ocr"
OCR_TIMEOUT = 30

# PDF处理配置
PDF_DOWNLOAD_TIMEOUT = 60
PDF_RENDER_DPI = 300

# 信息提取模式
EXTRACT_PATTERNS = {
    'name': r'案例教师姓名[：:\s]*([^\s\n\r]{2,10})',
    'phone': r'手机号码[：:\s]*(\d{11})',
    'id_card': r'身份证号[：:\s]*([0-9X]{15,18})'
}
```

### 程序化使用
```python
import importlib.util
from pathlib import Path

# 加载模块
module_path = Path("ocr/2025_zxxzhjy.py")
spec = importlib.util.spec_from_file_location("zxxzhjy_module", module_path)
ocr_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(ocr_module)

# 处理单个PDF
processor = ocr_module.OCRProcessor()
persons = processor.process_pdf_url("https://example.com/doc.pdf")

# 处理Excel文件
excel_processor = ocr_module.ExcelProcessor("input.xlsx")
result_df = excel_processor.process_excel()
excel_processor.save_results(result_df, "output.xlsx")
```

### 批量处理
```python
# 批量处理多个PDF
pdf_urls = [
    "https://example.com/doc1.pdf",
    "https://example.com/doc2.pdf",
    "https://example.com/doc3.pdf"
]

processor = ocr_module.OCRProcessor()
all_results = []

for url in pdf_urls:
    try:
        persons = processor.process_pdf_url(url)
        for person in persons:
            person['source_url'] = url
            all_results.append(person)
    except Exception as e:
        print(f"处理失败: {url}, 错误: {e}")

# 保存结果
import pandas as pd
df = pd.DataFrame(all_results)
df.to_excel("batch_results.xlsx", index=False)
```

## 故障排除

### 常见问题

1. **OCR服务连接失败**
   ```
   ❌ OCR服务连接失败 - 请确保服务运行在 http://127.0.0.1:1224/api/ocr
   ```
   解决方案：
   - 检查OCR服务是否启动
   - 验证服务地址和端口
   - 检查防火墙设置

2. **PDF下载失败**
   ```
   下载PDF失败: https://example.com/doc.pdf, 错误: Connection timeout
   ```
   解决方案：
   - 检查网络连接
   - 验证PDF URL有效性
   - 增加下载超时时间

3. **信息提取不准确**
   ```
   提取到 0 个有效人员信息
   ```
   解决方案：
   - 检查PDF格式是否符合要求
   - 调整信息提取正则表达式
   - 提高PDF渲染DPI

4. **内存不足**
   ```
   MemoryError: Unable to allocate array
   ```
   解决方案：
   - 分批处理大量PDF
   - 降低PDF渲染DPI
   - 增加系统内存

### 日志分析
查看 `ocr_processing.log` 文件了解详细处理过程：
```bash
# 查看最新日志
tail -f ocr_processing.log

# 搜索错误信息
grep "ERROR" ocr_processing.log

# 查看特定PDF的处理过程
grep "doc1.pdf" ocr_processing.log
```

### 性能优化
1. **提高OCR质量**：增加 `PDF_RENDER_DPI` 到 400-600
2. **加快处理速度**：降低 `PDF_RENDER_DPI` 到 200-250
3. **减少内存使用**：分批处理，避免同时处理大量PDF
4. **网络优化**：增加超时时间，使用稳定网络连接

## 测试和验证

### 运行测试
```bash
# 运行完整测试
python run_ocr.py --test

# 运行单独测试
python test_ocr.py

# 运行示例
python example.py
```

### 验证结果
1. 检查输出Excel文件的数据完整性
2. 验证提取的姓名、手机号、身份证号格式
3. 对比原始PDF确认信息准确性
4. 查看统计报告了解处理成功率

## 技术支持

如遇到问题，请提供以下信息：
1. 错误信息和日志文件
2. 输入Excel文件格式
3. PDF文件样例
4. 系统环境信息
5. OCR服务配置

联系开发团队获取技术支持。

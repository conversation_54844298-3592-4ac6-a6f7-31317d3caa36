#!/usr/bin/env python3
"""
检查OCR系统依赖的脚本
"""

import sys
import importlib
import subprocess
from typing import List, Tuple
import requests

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，建议使用Python 3.8+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True


def check_required_packages() -> List[Tuple[str, bool, str]]:
    """检查必需的Python包"""
    required_packages = [
        ('pandas', 'Excel文件处理'),
        ('openpyxl', 'Excel文件读写'),
        ('requests', 'HTTP请求'),
        ('pypdfium2', 'PDF处理'),
        ('PIL', 'Pillow图像处理'),
        ('base64', 'Base64编码'),
        ('logging', '日志记录'),
        ('re', '正则表达式'),
        ('os', '操作系统接口'),
        ('pathlib', '路径处理'),
    ]
    
    results = []
    print("\n检查Python包依赖...")
    
    for package, description in required_packages:
        try:
            if package == 'PIL':
                importlib.import_module('PIL')
            else:
                importlib.import_module(package)
            print(f"✅ {package} - {description}")
            results.append((package, True, description))
        except ImportError:
            print(f"❌ {package} - {description} (未安装)")
            results.append((package, False, description))
    
    return results


def check_ocr_service():
    """检查OCR服务连接"""
    print("\n检查OCR服务...")
    
    try:
        import requests
        response = requests.get('http://127.0.0.1:1224/api/ocr', timeout=5)
        if response.status_code == 200:
            print("✅ OCR服务连接正常")
            return True
        else:
            print(f"⚠️  OCR服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ OCR服务连接失败 - 请确保服务运行在 http://127.0.0.1:1224/api/ocr")
        return False
    except requests.exceptions.Timeout:
        print("❌ OCR服务连接超时")
        return False
    except ImportError:
        print("❌ requests包未安装，无法检查OCR服务")
        return False
    except Exception as e:
        print(f"❌ OCR服务检查失败: {e}")
        return False


def install_missing_packages(missing_packages: List[str]):
    """安装缺失的包"""
    if not missing_packages:
        return
    
    print(f"\n发现 {len(missing_packages)} 个缺失的包:")
    for package in missing_packages:
        print(f"  - {package}")
    
    install = input("\n是否自动安装缺失的包? (y/n): ").lower().strip()
    
    if install == 'y':
        for package in missing_packages:
            try:
                print(f"安装 {package}...")
                # 特殊处理PIL包名
                install_name = 'Pillow' if package == 'PIL' else package
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', install_name])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package} 安装失败: {e}")
    else:
        print("请手动安装缺失的包:")
        for package in missing_packages:
            install_name = 'Pillow' if package == 'PIL' else package
            print(f"  pip install {install_name}")


def check_file_structure():
    """检查文件结构"""
    print("\n检查文件结构...")
    
    import os
    required_files = [
        'ocr/2025_zxxzhjy.py',
        'config.py',
        'run_ocr.py',
        'test_ocr.py',
        'example.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (缺失)")
            all_exist = False
    
    return all_exist


def main():
    """主函数"""
    print("OCR系统依赖检查")
    print("=" * 50)
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查Python包
    package_results = check_required_packages()
    missing_packages = [pkg for pkg, installed, _ in package_results if not installed]
    
    # 检查文件结构
    files_ok = check_file_structure()
    
    # 检查OCR服务
    ocr_ok = check_ocr_service()
    
    # 总结
    print("\n" + "=" * 50)
    print("检查总结:")
    
    if python_ok:
        print("✅ Python版本正常")
    else:
        print("❌ Python版本需要升级")
    
    if not missing_packages:
        print("✅ 所有Python包已安装")
    else:
        print(f"❌ 缺失 {len(missing_packages)} 个Python包")
    
    if files_ok:
        print("✅ 文件结构完整")
    else:
        print("❌ 文件结构不完整")
    
    if ocr_ok:
        print("✅ OCR服务连接正常")
    else:
        print("❌ OCR服务连接失败")
    
    # 安装缺失的包
    if missing_packages:
        install_missing_packages(missing_packages)
    
    # 给出建议
    print("\n建议:")
    if not python_ok:
        print("- 升级Python到3.8或更高版本")
    
    if missing_packages:
        print("- 安装缺失的Python包")
    
    if not files_ok:
        print("- 检查文件是否完整下载")
    
    if not ocr_ok:
        print("- 启动OCR服务或检查服务地址配置")
        print("- 如果使用不同的OCR服务地址，请修改config.py中的OCR_API_URL")
    
    if python_ok and not missing_packages and files_ok and ocr_ok:
        print("🎉 所有依赖检查通过，系统可以正常使用!")
        print("运行 'python run_ocr.py --test' 进行功能测试")


if __name__ == "__main__":
    main()

"""
OCR系统配置文件
"""

import os
from typing import Dict, Any


class Config:
    """配置类"""
    
    # OCR API配置
    OCR_API_URL = os.getenv('OCR_API_URL', 'http://127.0.0.1:1224/api/ocr')
    OCR_TIMEOUT = int(os.getenv('OCR_TIMEOUT', '30'))
    
    # OCR请求配置
    OCR_OPTIONS = {
        "ocr.language": "models/config_chinese.txt",
        "ocr.cls": True,
        "ocr.limit_side_len": 4320,
        "tbpu.parser": "multi_none",
        "data.format": "text"
    }
    
    # PDF处理配置
    PDF_DOWNLOAD_TIMEOUT = int(os.getenv('PDF_DOWNLOAD_TIMEOUT', '60'))
    PDF_RENDER_DPI = int(os.getenv('PDF_RENDER_DPI', '300'))
    
    # 数据验证配置
    VALIDATE_NAME_PATTERN = r'^[\u4e00-\u9fa5]{2,10}$'
    VALIDATE_PHONE_PATTERN = r'^1[3-9]\d{9}$'
    VALIDATE_ID_CARD_PATTERN = r'^[0-9]{15}$|^[0-9]{17}[0-9X]$'
    
    # 信息提取模式配置
    EXTRACT_PATTERNS = {
        'name': r'案例教师姓名[：:\s]*([^\s\n\r]{2,10})',
        'phone': r'手机号码[：:\s]*(\d{11})',
        'id_card': r'身份证号[：:\s]*([0-9X]{15,18})'
    }
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'ocr_processing.log')
    
    # Excel处理配置
    EXCEL_URL_COLUMN_NAMES = ['tjb_url', 'url', 'pdf_url', 'download_url']
    
    @classmethod
    def get_ocr_options(cls) -> Dict[str, Any]:
        """获取OCR选项"""
        return cls.OCR_OPTIONS.copy()
    
    @classmethod
    def get_extract_patterns(cls) -> Dict[str, str]:
        """获取信息提取模式"""
        return cls.EXTRACT_PATTERNS.copy()
    
    @classmethod
    def update_from_env(cls):
        """从环境变量更新配置"""
        # 可以在这里添加更多环境变量的读取逻辑
        pass


# 全局配置实例
config = Config()

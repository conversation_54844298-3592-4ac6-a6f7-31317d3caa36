#!/usr/bin/env python3
"""
OCR系统使用示例
"""

import os
import pandas as pd
import importlib.util
from pathlib import Path


def load_ocr_module():
    """动态加载OCR模块"""
    module_path = Path(__file__).parent / "ocr" / "2025_zxxzhjy.py"
    spec = importlib.util.spec_from_file_location("zxxzhjy_module", module_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def create_sample_excel():
    """创建示例Excel文件"""
    sample_data = {
        'tjb_url': [
            'https://example.com/sample1.pdf',  # 请替换为真实的PDF URL
            'https://example.com/sample2.pdf',  # 请替换为真实的PDF URL
            'https://example.com/sample3.pdf',  # 请替换为真实的PDF URL
        ],
        'title': ['案例1', '案例2', '案例3'],
        'description': ['描述1', '描述2', '描述3']
    }
    
    df = pd.DataFrame(sample_data)
    sample_file = 'sample_input.xlsx'
    df.to_excel(sample_file, index=False)
    print(f"创建示例Excel文件: {sample_file}")
    return sample_file


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 加载OCR模块
    ocr_module = load_ocr_module()
    
    # 创建示例Excel文件
    excel_file = create_sample_excel()
    
    try:
        # 创建处理器
        processor = ocr_module.ExcelProcessor(excel_file)
        
        # 处理Excel文件
        print("开始处理Excel文件...")
        result_df = processor.process_excel()
        
        # 保存结果
        output_file = 'sample_output.xlsx'
        processor.save_results(result_df, output_file)
        
        print(f"处理完成，结果保存到: {output_file}")
        
    except Exception as e:
        print(f"处理失败: {e}")
    
    finally:
        # 清理示例文件
        if os.path.exists(excel_file):
            os.remove(excel_file)


def example_custom_config():
    """自定义配置示例"""
    print("\n=== 自定义配置示例 ===")
    
    # 加载OCR模块
    ocr_module = load_ocr_module()
    
    # 创建自定义OCR处理器
    custom_processor = ocr_module.OCRProcessor(
        ocr_api_url="http://localhost:8080/api/ocr"  # 自定义API地址
    )
    
    print(f"自定义OCR API地址: {custom_processor.ocr_api_url}")
    print(f"OCR选项: {custom_processor.ocr_options}")


def example_single_pdf():
    """单个PDF处理示例"""
    print("\n=== 单个PDF处理示例 ===")
    
    # 加载OCR模块
    ocr_module = load_ocr_module()
    
    # 创建OCR处理器
    processor = ocr_module.OCRProcessor()
    
    # 处理单个PDF URL
    pdf_url = "https://example.com/sample.pdf"  # 请替换为真实的PDF URL
    
    try:
        print(f"处理PDF: {pdf_url}")
        persons = processor.process_pdf_url(pdf_url)
        
        print(f"提取到 {len(persons)} 个人员信息:")
        for i, person in enumerate(persons, 1):
            print(f"  人员 {i}:")
            print(f"    姓名: {person.get('name', 'N/A')}")
            print(f"    手机号: {person.get('phone', 'N/A')}")
            print(f"    身份证号: {person.get('id_card', 'N/A')}")
    
    except Exception as e:
        print(f"处理失败: {e}")


def example_batch_processing():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===")
    
    # 加载OCR模块
    ocr_module = load_ocr_module()
    
    # PDF URL列表
    pdf_urls = [
        "https://example.com/batch1.pdf",  # 请替换为真实的PDF URL
        "https://example.com/batch2.pdf",  # 请替换为真实的PDF URL
        "https://example.com/batch3.pdf",  # 请替换为真实的PDF URL
    ]
    
    # 创建OCR处理器
    processor = ocr_module.OCRProcessor()
    
    all_results = []
    
    for i, pdf_url in enumerate(pdf_urls, 1):
        try:
            print(f"处理第 {i}/{len(pdf_urls)} 个PDF: {pdf_url}")
            persons = processor.process_pdf_url(pdf_url)
            
            for person in persons:
                result = {
                    'source_url': pdf_url,
                    'name': person.get('name', ''),
                    'phone': person.get('phone', ''),
                    'id_card': person.get('id_card', '')
                }
                all_results.append(result)
        
        except Exception as e:
            print(f"处理失败: {e}")
    
    # 保存批量处理结果
    if all_results:
        df = pd.DataFrame(all_results)
        output_file = 'batch_results.xlsx'
        df.to_excel(output_file, index=False)
        print(f"批量处理完成，结果保存到: {output_file}")
    else:
        print("没有提取到任何数据")


def main():
    """主函数"""
    print("OCR系统使用示例")
    print("=" * 50)
    
    # 检查OCR服务
    print("请确保OCR服务运行在 http://127.0.0.1:1224/api/ocr")
    print("如果使用不同的地址，请修改配置文件或传入自定义参数")
    print()
    
    # 运行示例
    example_basic_usage()
    example_custom_config()
    example_single_pdf()
    example_batch_processing()
    
    print("\n示例运行完成!")
    print("注意: 示例中的PDF URL需要替换为真实可访问的地址")


if __name__ == "__main__":
    main()

"""
应用2025年广东省国家中小学智慧教育平台应用优质教育教学教研案 OCR识别
资源审核列表-已通过数据-盖章版推荐表 姓名+手机号+身份证号

功能：处理Excel文件，对其中包含PDF下载链接的每一行进行OCR识别，提取人员信息
"""

import os
import re
import base64
import logging
import tempfile
from io import BytesIO
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urlparse
from pathlib import Path

import requests
import pandas as pd
from PIL import Image
import pypdfium2
from openpyxl import load_workbook

# 导入配置
try:
    from config import config
except ImportError:
    # 如果配置文件不存在，使用默认配置
    class DefaultConfig:
        OCR_API_URL = 'http://127.0.0.1:1224/api/ocr'
        OCR_TIMEOUT = 30
        PDF_DOWNLOAD_TIMEOUT = 60
        PDF_RENDER_DPI = 300
        LOG_LEVEL = 'INFO'
        LOG_FILE = 'ocr_processing.log'
        EXCEL_URL_COLUMN_NAMES = ['tjb_url', 'url', 'pdf_url', 'download_url']

        OCR_OPTIONS = {
            "ocr.language": "models/config_chinese.txt",
            "ocr.cls": True,
            "ocr.limit_side_len": 4320,
            "tbpu.parser": "multi_none",
            "data.format": "text"
        }

        EXTRACT_PATTERNS = {
            'name': r'案例教师姓名[：:\s]*([^\s\n\r]{2,10})',
            'phone': r'手机号码[：:\s]*(\d{11})',
            'id_card': r'身份证号[：:\s]*([0-9X]{15,18})'
        }

        VALIDATE_NAME_PATTERN = r'^[\u4e00-\u9fa5]{2,10}$'
        VALIDATE_PHONE_PATTERN = r'^1[3-9]\d{9}$'
        VALIDATE_ID_CARD_PATTERN = r'^[0-9]{15}$|^[0-9]{17}[0-9X]$'

    config = DefaultConfig()

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL.upper()),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class OCRProcessor:
    """OCR处理器类，负责PDF文件的OCR识别和信息提取"""

    def __init__(self, ocr_api_url: str = None):
        """
        初始化OCR处理器

        Args:
            ocr_api_url: OCR API接口地址，如果为None则使用配置文件中的地址
        """
        self.ocr_api_url = ocr_api_url or config.OCR_API_URL
        self.session = requests.Session()
        self.session.timeout = config.OCR_TIMEOUT

        # OCR请求的配置
        self.ocr_options = config.OCR_OPTIONS.copy()

        # 信息提取模式
        self.extract_patterns = config.EXTRACT_PATTERNS.copy()

    def download_pdf(self, url: str) -> bytes:
        """
        下载PDF文件

        Args:
            url: PDF文件的下载链接

        Returns:
            PDF文件的二进制数据

        Raises:
            requests.RequestException: 下载失败时抛出异常
        """
        try:
            logger.info(f"开始下载PDF: {url}")
            response = self.session.get(url, timeout=config.PDF_DOWNLOAD_TIMEOUT)
            response.raise_for_status()

            # 验证是否为PDF文件
            content_type = response.headers.get('content-type', '').lower()
            if 'pdf' not in content_type and not url.lower().endswith('.pdf'):
                logger.warning(f"文件可能不是PDF格式: {content_type}")

            logger.info(f"PDF下载成功，大小: {len(response.content)} bytes")
            return response.content

        except requests.RequestException as e:
            logger.error(f"下载PDF失败: {url}, 错误: {str(e)}")
            raise

    def pdf_to_images(self, pdf_content: bytes) -> List[bytes]:
        """
        将PDF转换为图片列表

        Args:
            pdf_content: PDF文件的二进制数据

        Returns:
            图片数据列表（PNG格式的bytes）

        Raises:
            Exception: PDF处理失败时抛出异常
        """
        try:
            logger.info("开始将PDF转换为图片")
            images = []

            # 使用pypdfium2处理PDF
            pdf_file = BytesIO(pdf_content)
            pdf_document = pypdfium2.PdfDocument(pdf_file, autoclose=True)

            try:
                for page_num, page in enumerate(pdf_document):
                    logger.debug(f"处理第 {page_num + 1} 页")

                    # 渲染页面为图片 (使用配置的DPI for better OCR quality)
                    bitmap = page.render(scale=config.PDF_RENDER_DPI/72)
                    pil_image = bitmap.to_pil()

                    # 转换为PNG格式的bytes
                    img_buffer = BytesIO()
                    pil_image.save(img_buffer, format='PNG', optimize=True)
                    images.append(img_buffer.getvalue())

                    # 清理资源
                    bitmap.close()
                    page.close()

            finally:
                pdf_document.close()

            logger.info(f"PDF转换完成，共 {len(images)} 页")
            return images

        except Exception as e:
            logger.error(f"PDF转图片失败: {str(e)}")
            raise

    def image_to_base64(self, image_data: bytes) -> str:
        """
        将图片数据转换为base64编码

        Args:
            image_data: 图片的二进制数据

        Returns:
            base64编码的字符串
        """
        return base64.b64encode(image_data).decode('utf-8')

    def call_ocr_api(self, base64_image: str) -> str:
        """
        调用OCR API进行文字识别

        Args:
            base64_image: base64编码的图片数据

        Returns:
            OCR识别的文本结果

        Raises:
            requests.RequestException: API调用失败时抛出异常
        """
        try:
            payload = {
                "base64": base64_image,
                "options": self.ocr_options
            }

            logger.debug("调用OCR API")
            response = self.session.post(
                self.ocr_api_url,
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            response.raise_for_status()

            result = response.json()

            # 根据API返回格式提取文本
            if 'data' in result and 'text' in result['data']:
                text = result['data']['text']
            elif 'text' in result:
                text = result['text']
            elif isinstance(result, str):
                text = result
            else:
                logger.warning(f"未知的OCR API响应格式: {result}")
                text = str(result)

            logger.debug(f"OCR识别完成，文本长度: {len(text)}")
            return text

        except requests.RequestException as e:
            logger.error(f"OCR API调用失败: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"OCR结果处理失败: {str(e)}")
            raise

    def extract_person_info(self, ocr_text: str) -> List[Dict[str, str]]:
        """
        从OCR文本中提取人员信息

        Args:
            ocr_text: OCR识别的文本

        Returns:
            人员信息列表，每个元素包含姓名、身份证号、手机号
        """
        logger.debug("开始提取人员信息")

        # 清理文本，移除多余的空白字符
        text = re.sub(r'\s+', ' ', ocr_text.strip())

        # 使用配置的提取规则
        patterns = self.extract_patterns

        # 尝试多种匹配策略
        persons = []

        # 策略1: 直接匹配所有信息
        names = re.findall(patterns['name'], text, re.IGNORECASE)
        phones = re.findall(patterns['phone'], text, re.IGNORECASE)
        id_cards = re.findall(patterns['id_card'], text, re.IGNORECASE)

        logger.debug(f"找到姓名: {names}, 手机号: {phones}, 身份证: {id_cards}")

        # 如果三类信息数量一致，按顺序配对
        if len(names) == len(phones) == len(id_cards) and len(names) > 0:
            for i in range(len(names)):
                person = {
                    'name': names[i].strip(),
                    'phone': phones[i].strip(),
                    'id_card': id_cards[i].strip()
                }
                if self._validate_person_info(person):
                    persons.append(person)

        # 策略2: 如果数量不一致，尝试基于位置的匹配
        elif names or phones or id_cards:
            logger.warning("信息数量不一致，尝试基于位置的匹配")

            # 找到所有匹配项及其位置
            all_matches = []

            for match in re.finditer(patterns['name'], text, re.IGNORECASE):
                all_matches.append(('name', match.group(1).strip(), match.start()))

            for match in re.finditer(patterns['phone'], text, re.IGNORECASE):
                all_matches.append(('phone', match.group(1).strip(), match.start()))

            for match in re.finditer(patterns['id_card'], text, re.IGNORECASE):
                all_matches.append(('id_card', match.group(1).strip(), match.start()))

            # 按位置排序
            all_matches.sort(key=lambda x: x[2])

            # 尝试将相邻的信息组合成人员记录
            current_person = {}
            for info_type, value, position in all_matches:
                current_person[info_type] = value

                # 如果收集到完整信息，保存并开始新的记录
                if len(current_person) == 3:
                    if self._validate_person_info(current_person):
                        persons.append(current_person.copy())
                    current_person = {}

            # 处理最后一个不完整的记录
            if len(current_person) == 3:
                if self._validate_person_info(current_person):
                    persons.append(current_person)

        logger.info(f"提取到 {len(persons)} 个有效人员信息")
        return persons

    def _validate_person_info(self, person: Dict[str, str]) -> bool:
        """
        验证人员信息的有效性

        Args:
            person: 包含姓名、手机号、身份证号的字典

        Returns:
            信息是否有效
        """
        # 检查必要字段
        if not all(key in person for key in ['name', 'phone', 'id_card']):
            return False

        name = person['name']
        phone = person['phone']
        id_card = person['id_card']

        # 验证姓名（2-10个中文字符）
        if not re.match(config.VALIDATE_NAME_PATTERN, name):
            logger.warning(f"无效姓名: {name}")
            return False

        # 验证手机号（11位数字）
        if not re.match(config.VALIDATE_PHONE_PATTERN, phone):
            logger.warning(f"无效手机号: {phone}")
            return False

        # 验证身份证号（15位或18位）
        if not re.match(config.VALIDATE_ID_CARD_PATTERN, id_card.upper()):
            logger.warning(f"无效身份证号: {id_card}")
            return False

        return True

    def process_pdf_url(self, pdf_url: str) -> List[Dict[str, str]]:
        """
        处理单个PDF URL，返回提取的人员信息

        Args:
            pdf_url: PDF文件的下载链接

        Returns:
            人员信息列表
        """
        try:
            logger.info(f"开始处理PDF: {pdf_url}")

            # 下载PDF
            pdf_content = self.download_pdf(pdf_url)

            # 转换为图片
            images = self.pdf_to_images(pdf_content)

            # 对每页进行OCR识别
            all_persons = []
            for page_num, image_data in enumerate(images):
                logger.info(f"处理第 {page_num + 1} 页")

                # 转换为base64
                base64_image = self.image_to_base64(image_data)

                # OCR识别
                ocr_text = self.call_ocr_api(base64_image)

                # 提取人员信息
                persons = self.extract_person_info(ocr_text)
                all_persons.extend(persons)

            # 去重（基于身份证号）
            unique_persons = []
            seen_ids = set()
            for person in all_persons:
                if person['id_card'] not in seen_ids:
                    unique_persons.append(person)
                    seen_ids.add(person['id_card'])

            logger.info(f"PDF处理完成，提取到 {len(unique_persons)} 个唯一人员信息")
            return unique_persons

        except Exception as e:
            logger.error(f"处理PDF失败: {pdf_url}, 错误: {str(e)}")
            return []


class ExcelProcessor:
    """Excel文件处理器"""

    def __init__(self, excel_path: str):
        """
        初始化Excel处理器

        Args:
            excel_path: Excel文件路径
        """
        self.excel_path = excel_path
        self.ocr_processor = OCRProcessor()

    def load_excel(self) -> pd.DataFrame:
        """
        加载Excel文件

        Returns:
            pandas DataFrame

        Raises:
            Exception: 文件加载失败时抛出异常
        """
        try:
            logger.info(f"加载Excel文件: {self.excel_path}")

            # 尝试不同的读取方式
            try:
                df = pd.read_excel(self.excel_path, engine='openpyxl')
            except Exception:
                df = pd.read_excel(self.excel_path, engine='xlrd')

            logger.info(f"Excel加载成功，共 {len(df)} 行数据")
            return df

        except Exception as e:
            logger.error(f"Excel文件加载失败: {str(e)}")
            raise

    def find_url_column(self, df: pd.DataFrame) -> str:
        """
        查找包含PDF URL的列

        Args:
            df: pandas DataFrame

        Returns:
            列名

        Raises:
            ValueError: 找不到URL列时抛出异常
        """
        # 按优先级查找URL列
        for col_name in config.EXCEL_URL_COLUMN_NAMES:
            if col_name in df.columns:
                logger.info(f"找到URL列: {col_name}")
                return col_name

        # 查找包含 'url' 的列名
        url_columns = [col for col in df.columns if 'url' in col.lower()]
        if url_columns:
            logger.info(f"找到URL列: {url_columns[0]}")
            return url_columns[0]

        # 查找包含HTTP链接的列
        for col in df.columns:
            if df[col].dtype == 'object':  # 字符串类型
                sample_values = df[col].dropna().head(10)
                if any(str(val).startswith(('http://', 'https://')) for val in sample_values):
                    logger.info(f"根据内容识别URL列: {col}")
                    return col

        raise ValueError("未找到包含PDF URL的列，请确保Excel文件包含 'tjb_url' 列或其他URL列")

    def process_excel(self) -> pd.DataFrame:
        """
        处理Excel文件，对每行的PDF进行OCR识别

        Returns:
            包含处理结果的DataFrame
        """
        try:
            # 加载Excel
            df = self.load_excel()

            # 查找URL列
            url_column = self.find_url_column(df)

            # 添加结果列
            df['ocr_persons'] = None
            df['person_count'] = 0
            df['processing_status'] = 'pending'
            df['error_message'] = ''

            # 处理每一行
            total_rows = len(df)
            for index, row in df.iterrows():
                logger.info(f"处理第 {index + 1}/{total_rows} 行")

                pdf_url = row[url_column]
                if pd.isna(pdf_url) or not str(pdf_url).strip():
                    df.at[index, 'processing_status'] = 'skipped'
                    df.at[index, 'error_message'] = 'URL为空'
                    continue

                try:
                    # 处理PDF
                    persons = self.ocr_processor.process_pdf_url(str(pdf_url).strip())

                    # 保存结果
                    df.at[index, 'ocr_persons'] = persons
                    df.at[index, 'person_count'] = len(persons)
                    df.at[index, 'processing_status'] = 'success'

                    logger.info(f"第 {index + 1} 行处理成功，提取到 {len(persons)} 个人员信息")

                except Exception as e:
                    error_msg = str(e)
                    df.at[index, 'processing_status'] = 'failed'
                    df.at[index, 'error_message'] = error_msg
                    logger.error(f"第 {index + 1} 行处理失败: {error_msg}")

            return df

        except Exception as e:
            logger.error(f"Excel处理失败: {str(e)}")
            raise

    def save_results(self, df: pd.DataFrame, output_path: str):
        """
        保存处理结果

        Args:
            df: 包含处理结果的DataFrame
            output_path: 输出文件路径
        """
        try:
            logger.info(f"保存结果到: {output_path}")

            # 创建详细结果DataFrame
            detailed_results = []

            for index, row in df.iterrows():
                base_info = {
                    'original_row': index + 1,
                    'pdf_url': row.get('tjb_url', ''),
                    'processing_status': row.get('processing_status', ''),
                    'error_message': row.get('error_message', ''),
                    'person_count': row.get('person_count', 0)
                }

                persons = row.get('ocr_persons', [])
                if persons:
                    for person_index, person in enumerate(persons):
                        result_row = base_info.copy()
                        result_row.update({
                            'person_index': person_index + 1,
                            'name': person.get('name', ''),
                            'phone': person.get('phone', ''),
                            'id_card': person.get('id_card', '')
                        })
                        detailed_results.append(result_row)
                else:
                    # 没有提取到人员信息的行也要保留
                    result_row = base_info.copy()
                    result_row.update({
                        'person_index': 0,
                        'name': '',
                        'phone': '',
                        'id_card': ''
                    })
                    detailed_results.append(result_row)

            # 保存详细结果
            result_df = pd.DataFrame(detailed_results)
            result_df.to_excel(output_path, index=False, engine='openpyxl')

            # 生成统计报告
            stats = {
                'total_rows': len(df),
                'success_rows': len(df[df['processing_status'] == 'success']),
                'failed_rows': len(df[df['processing_status'] == 'failed']),
                'skipped_rows': len(df[df['processing_status'] == 'skipped']),
                'total_persons': df['person_count'].sum()
            }

            logger.info("处理统计:")
            logger.info(f"  总行数: {stats['total_rows']}")
            logger.info(f"  成功: {stats['success_rows']}")
            logger.info(f"  失败: {stats['failed_rows']}")
            logger.info(f"  跳过: {stats['skipped_rows']}")
            logger.info(f"  提取人员总数: {stats['total_persons']}")

            # 保存统计报告
            stats_path = output_path.replace('.xlsx', '_stats.txt')
            with open(stats_path, 'w', encoding='utf-8') as f:
                f.write("OCR处理统计报告\n")
                f.write("=" * 30 + "\n")
                f.write(f"总行数: {stats['total_rows']}\n")
                f.write(f"成功处理: {stats['success_rows']}\n")
                f.write(f"处理失败: {stats['failed_rows']}\n")
                f.write(f"跳过处理: {stats['skipped_rows']}\n")
                f.write(f"提取人员总数: {stats['total_persons']}\n")
                f.write(f"成功率: {stats['success_rows']/stats['total_rows']*100:.1f}%\n")

            logger.info(f"结果保存完成: {output_path}")
            logger.info(f"统计报告保存: {stats_path}")

        except Exception as e:
            logger.error(f"保存结果失败: {str(e)}")
            raise


def main(excel_path: str = None, output_path: str = None):
    """
    主函数

    Args:
        excel_path: Excel文件路径，如果为None则使用默认路径
        output_path: 输出文件路径，如果为None则自动生成
    """
    try:
        # 设置默认路径
        if excel_path is None:
            excel_path = input("请输入Excel文件路径: ").strip()

        if not os.path.exists(excel_path):
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")

        if output_path is None:
            # 自动生成输出路径
            base_name = os.path.splitext(os.path.basename(excel_path))[0]
            output_dir = os.path.dirname(excel_path)
            output_path = os.path.join(output_dir, f"{base_name}_ocr_results.xlsx")

        logger.info("开始处理Excel文件")
        logger.info(f"输入文件: {excel_path}")
        logger.info(f"输出文件: {output_path}")

        # 创建处理器并执行处理
        processor = ExcelProcessor(excel_path)
        result_df = processor.process_excel()
        processor.save_results(result_df, output_path)

        logger.info("处理完成!")

    except KeyboardInterrupt:
        logger.info("用户中断处理")
    except Exception as e:
        logger.error(f"处理失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()

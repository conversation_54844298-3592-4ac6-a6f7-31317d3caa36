"""
应用2025年广东省国家中小学智慧教育平台应用优质教育教学教研案 OCR识别
资源审核列表-已通过数据-盖章版推荐表 姓名+手机号+身份证号

功能：处理Excel文件，对其中包含PDF下载链接的每一行进行OCR识别，提取人员信息
"""

import base64
import logging
import os
import re
from io import BytesIO
from typing import List, Dict, Any, Optional, Tuple

import pandas as pd
import pypdfium2
import requests

# 导入配置
try:
    from config import config
except ImportError:
    # 如果配置文件不存在，使用默认配置
    class DefaultConfig:
        OCR_API_URL = 'http://8.138.85.174:1224/api/ocr'
        OCR_TIMEOUT = 30
        PDF_DOWNLOAD_TIMEOUT = 60
        PDF_RENDER_DPI = 300
        LOG_LEVEL = 'INFO'
        LOG_FILE = 'ocr_processing.log'
        EXCEL_URL_COLUMN_NAMES = ['tjb_url', 'url', 'pdf_url', 'download_url']

        OCR_OPTIONS = {
            "ocr.language": "models/config_chinese.txt",
            "ocr.cls": True,
            "ocr.limit_side_len": 999999,
            "tbpu.parser": "multi_none",
            # 不设置 "data.format" 以获取结构化数据
        }

        USE_STRUCTURED_OCR = True
        OCR_CONFIDENCE_THRESHOLD = 0.5
        SPATIAL_MATCHING_ENABLED = True

        EXTRACT_PATTERNS = {
            'name': r'案例教师姓名[：:\s]*([^\s\n\r]{2,10})',
            'phone': r'手机号码[：:\s]*(\d{11})',
            'id_card': r'身份证号[：:\s]*([0-9X]{15,18})'
        }

        VALIDATE_NAME_PATTERN = r'^[\u4e00-\u9fa5]{2,10}$'
        VALIDATE_PHONE_PATTERN = r'^1[3-9]\d{9}$'
        VALIDATE_ID_CARD_PATTERN = r'^[0-9]{15}$|^[0-9]{17}[0-9X]$'

    config = DefaultConfig()

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL.upper()),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class OCRProcessor:
    """OCR处理器类，负责PDF文件的OCR识别和信息提取"""

    def __init__(self, ocr_api_url: str = None):
        """
        初始化OCR处理器

        Args:
            ocr_api_url: OCR API接口地址，如果为None则使用配置文件中的地址
        """
        self.ocr_api_url = ocr_api_url or config.OCR_API_URL
        self.session = requests.Session()
        self.session.timeout = config.OCR_TIMEOUT

        # OCR请求的配置
        self.ocr_options = config.OCR_OPTIONS.copy()

        # 信息提取模式
        self.extract_patterns = config.EXTRACT_PATTERNS.copy()

        # 启用结构化OCR数据处理
        self.use_structured_ocr = True

    def download_pdf(self, url: str) -> bytes:
        """
        下载PDF文件

        Args:
            url: PDF文件的下载链接

        Returns:
            PDF文件的二进制数据

        Raises:
            requests.RequestException: 下载失败时抛出异常
        """
        try:
            logger.info(f"开始下载PDF: {url}")
            response = self.session.get(url, timeout=config.PDF_DOWNLOAD_TIMEOUT)
            response.raise_for_status()

            # 验证是否为PDF文件
            content_type = response.headers.get('content-type', '').lower()
            if 'pdf' not in content_type and not url.lower().endswith('.pdf'):
                logger.warning(f"文件可能不是PDF格式: {content_type}")

            logger.info(f"PDF下载成功，大小: {len(response.content)} bytes")
            return response.content

        except requests.RequestException as e:
            logger.error(f"下载PDF失败: {url}, 错误: {str(e)}")
            raise

    def pdf_to_images(self, pdf_content: bytes) -> List[bytes]:
        """
        将PDF转换为图片列表

        Args:
            pdf_content: PDF文件的二进制数据

        Returns:
            图片数据列表（PNG格式的bytes）

        Raises:
            Exception: PDF处理失败时抛出异常
        """
        try:
            logger.info("开始将PDF转换为图片")
            images = []

            # 使用pypdfium2处理PDF
            pdf_file = BytesIO(pdf_content)
            pdf_document = pypdfium2.PdfDocument(pdf_file, autoclose=True)

            try:
                for page_num, page in enumerate(pdf_document):
                    logger.debug(f"处理第 {page_num + 1} 页")

                    # 渲染页面为图片 (使用配置的DPI for better OCR quality)
                    bitmap = page.render(scale=config.PDF_RENDER_DPI/72)
                    pil_image = bitmap.to_pil()

                    # 转换为PNG格式的bytes
                    img_buffer = BytesIO()
                    pil_image.save(img_buffer, format='PNG', optimize=True)
                    images.append(img_buffer.getvalue())

                    # 清理资源
                    bitmap.close()
                    page.close()

            finally:
                pdf_document.close()

            logger.info(f"PDF转换完成，共 {len(images)} 页")
            return images

        except Exception as e:
            logger.error(f"PDF转图片失败: {str(e)}")
            raise

    def image_to_base64(self, image_data: bytes) -> str:
        """
        将图片数据转换为base64编码

        Args:
            image_data: 图片的二进制数据

        Returns:
            base64编码的字符串
        """
        return base64.b64encode(image_data).decode('utf-8')

    def call_ocr_api(self, base64_image: str, get_structured_data: bool = None) -> Dict[str, Any]:
        """
        调用OCR API进行文字识别

        Args:
            base64_image: base64编码的图片数据
            get_structured_data: 是否获取结构化数据，None时使用实例配置

        Returns:
            OCR识别结果，包含文本和结构化数据

        Raises:
            requests.RequestException: API调用失败时抛出异常
        """
        try:
            # 决定是否使用结构化数据
            use_structured = get_structured_data if get_structured_data is not None else self.use_structured_ocr

            # 准备OCR选项
            ocr_options = self.ocr_options.copy()
            if use_structured:
                # 移除 data.format 选项以获取结构化数据
                ocr_options.pop('data.format', None)

            payload = {
                "base64": base64_image,
                "options": ocr_options
            }

            logger.debug("调用OCR API")
            response = self.session.post(
                self.ocr_api_url,
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            response.raise_for_status()

            result = response.json()

            # 处理OCR响应
            ocr_result = {
                'raw_response': result,
                'text': '',
                'structured_data': None,
                'success': True
            }

            # 提取文本和结构化数据
            if 'data' in result:
                data = result['data']
                if isinstance(data, list):
                    # 结构化数据格式
                    ocr_result['structured_data'] = data
                    ocr_result['text'] = self._extract_text_from_structured_data(data)
                elif isinstance(data, str):
                    # 纯文本格式
                    ocr_result['text'] = data
                elif isinstance(data, dict) and 'text' in data:
                    ocr_result['text'] = data['text']
                else:
                    logger.warning(f"未知的OCR数据格式: {type(data)}")
                    ocr_result['text'] = str(data)
            elif 'text' in result:
                ocr_result['text'] = result['text']
            elif isinstance(result, str):
                ocr_result['text'] = result
            else:
                logger.warning(f"未知的OCR API响应格式: {result}")
                ocr_result['text'] = str(result)
                ocr_result['success'] = False

            logger.debug(f"OCR识别完成，文本长度: {len(ocr_result['text'])}, 结构化数据: {ocr_result['structured_data'] is not None}")
            return ocr_result

        except requests.RequestException as e:
            logger.error(f"OCR API调用失败: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"OCR结果处理失败: {str(e)}")
            raise

    def _extract_text_from_structured_data(self, structured_data: List[Dict]) -> str:
        """
        从结构化OCR数据中提取纯文本

        Args:
            structured_data: OCR返回的结构化数据列表

        Returns:
            拼接的纯文本
        """
        text_parts = []
        for item in structured_data:
            if isinstance(item, dict) and 'text' in item:
                text_parts.append(item['text'])
                # 添加换行符（如果OCR数据中有end标记）
                if item.get('end') == '\n':
                    text_parts.append('\n')

        return ''.join(text_parts)

    def extract_person_info(self, ocr_result: Any) -> List[Dict[str, str]]:
        """
        从OCR结果中提取人员信息

        Args:
            ocr_result: OCR识别结果，可以是字符串（向后兼容）或OCR结果字典

        Returns:
            人员信息列表，每个元素包含姓名、身份证号、手机号
        """
        logger.debug("开始提取人员信息")

        # 处理输入参数，支持向后兼容
        if isinstance(ocr_result, str):
            # 向后兼容：纯文本输入
            ocr_data = {
                'text': ocr_result,
                'structured_data': None,
                'success': True
            }
        elif isinstance(ocr_result, dict):
            ocr_data = ocr_result
        else:
            logger.error(f"不支持的OCR结果类型: {type(ocr_result)}")
            return []

        # 尝试多种提取策略
        persons = []

        # 策略1: 基于坐标的空间位置匹配（优先）
        if ocr_data.get('structured_data'):
            logger.debug("使用策略1: 基于坐标的空间位置匹配")
            persons = self._extract_by_spatial_matching(ocr_data['structured_data'])

        # 策略2: 改进的正则表达式跨行匹配（备选）
        if not persons and ocr_data.get('text'):
            logger.debug("使用策略2: 改进的正则表达式跨行匹配")
            persons = self._extract_by_enhanced_regex(ocr_data['text'])

        # 策略3: 传统正则表达式匹配（兜底）
        if not persons and ocr_data.get('text'):
            logger.debug("使用策略3: 传统正则表达式匹配")
            persons = self._extract_by_traditional_regex(ocr_data['text'])

        logger.info(f"提取到 {len(persons)} 个有效人员信息")
        return persons

    def _extract_by_spatial_matching(self, structured_data: List[Dict]) -> List[Dict[str, str]]:
        """
        基于坐标的空间位置匹配提取人员信息

        Args:
            structured_data: OCR返回的结构化数据

        Returns:
            提取的人员信息列表
        """
        logger.debug("开始基于坐标的空间位置匹配")

        # 过滤低置信度的文本块
        filtered_data = []
        for item in structured_data:
            if isinstance(item, dict) and 'text' in item:
                score = item.get('score', 1.0)
                if score >= 0.5:  # 置信度阈值
                    filtered_data.append(item)
                else:
                    logger.debug(f"过滤低置信度文本: {item['text']} (score: {score})")

        # 查找标识文本
        label_items = self._find_label_items(filtered_data)
        logger.debug(f"找到标识文本: {[item['text'] for item in label_items]}")

        # 为每个标识文本查找对应的数据
        persons = []
        current_person = {}

        for label_item in label_items:
            label_text = label_item['text']
            label_type = self._classify_label(label_text)

            if label_type:
                # 查找与标识文本空间相关的数据文本
                data_items = self._find_related_data_items(label_item, filtered_data)

                if data_items:
                    # 合并和清理数据文本
                    combined_text = self._combine_and_clean_text(data_items)

                    # 验证和提取具体数据
                    extracted_value = self._extract_and_validate_value(combined_text, label_type)

                    if extracted_value:
                        current_person[label_type] = extracted_value
                        logger.debug(f"提取 {label_type}: {extracted_value}")

        # 如果收集到完整信息，添加到结果中
        if len(current_person) >= 2:  # 至少需要两个字段
            if self._validate_person_info(current_person):
                persons.append(current_person)

        return persons

    def _find_label_items(self, structured_data: List[Dict]) -> List[Dict]:
        """
        查找标识文本项

        Args:
            structured_data: 过滤后的结构化数据

        Returns:
            包含标识文本的项列表
        """
        label_items = []
        label_keywords = ['案例教师姓名', '姓名', '手机号码', '手机号', '身份证号', '身份证']

        for item in structured_data:
            text = item.get('text', '').strip()
            for keyword in label_keywords:
                if keyword in text:
                    label_items.append(item)
                    break

        return label_items

    def _classify_label(self, label_text: str) -> Optional[str]:
        """
        分类标识文本

        Args:
            label_text: 标识文本

        Returns:
            分类结果: 'name', 'phone', 'id_card' 或 None
        """
        text = label_text.strip()

        if '姓名' in text:
            return 'name'
        elif '手机' in text:
            return 'phone'
        elif '身份证' in text:
            return 'id_card'

        return None

    def _find_related_data_items(self, label_item: Dict, all_items: List[Dict]) -> List[Dict]:
        """
        查找与标识文本空间相关的数据项

        Args:
            label_item: 标识文本项
            all_items: 所有文本项

        Returns:
            相关的数据项列表
        """
        if 'box' not in label_item:
            return []

        label_box = label_item['box']
        label_center_x, label_center_y = self._get_box_center(label_box)
        label_right = max(point[0] for point in label_box)
        label_bottom = max(point[1] for point in label_box)

        related_items = []

        for item in all_items:
            if item == label_item or 'box' not in item:
                continue

            item_box = item['box']
            item_center_x, item_center_y = self._get_box_center(item_box)
            item_left = min(point[0] for point in item_box)
            item_top = min(point[1] for point in item_box)

            # 判断空间关系
            is_right_adjacent = (
                item_left > label_right and  # 在标识文本右侧
                abs(item_center_y - label_center_y) < 50  # 垂直位置相近
            )

            is_below_adjacent = (
                item_top > label_bottom and  # 在标识文本下方
                abs(item_center_x - label_center_x) < 100 and  # 水平位置相近
                item_top - label_bottom < 100  # 距离不太远
            )

            if is_right_adjacent or is_below_adjacent:
                related_items.append(item)

        # 按位置排序（先右侧，再下方）
        related_items.sort(key=lambda x: (self._get_box_center(x['box'])[1], self._get_box_center(x['box'])[0]))

        return related_items

    def _get_box_center(self, box: List[List[int]]) -> Tuple[float, float]:
        """
        计算文本框的中心坐标

        Args:
            box: 文本框坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]

        Returns:
            中心坐标 (center_x, center_y)
        """
        if not box or len(box) != 4:
            return (0, 0)

        x_coords = [point[0] for point in box]
        y_coords = [point[1] for point in box]

        center_x = sum(x_coords) / len(x_coords)
        center_y = sum(y_coords) / len(y_coords)

        return (center_x, center_y)

    def _combine_and_clean_text(self, data_items: List[Dict]) -> str:
        """
        合并和清理数据文本

        Args:
            data_items: 数据项列表

        Returns:
            清理后的合并文本
        """
        texts = []
        for item in data_items:
            text = item.get('text', '').strip()
            if text:
                texts.append(text)

        combined = ''.join(texts)

        # 清理文本：移除表格边框字符、多余空格等
        cleaned = re.sub(r'[|\-_=+\s]+', '', combined)  # 移除表格边框字符和空格
        cleaned = re.sub(r'[^\u4e00-\u9fa5\w]', '', cleaned)  # 只保留中文、字母、数字

        return cleaned

    def _extract_and_validate_value(self, text: str, value_type: str) -> Optional[str]:
        """
        从文本中提取并验证特定类型的值

        Args:
            text: 输入文本
            value_type: 值类型 ('name', 'phone', 'id_card')

        Returns:
            提取的值，如果无效则返回None
        """
        if not text:
            return None

        if value_type == 'name':
            # 提取姓名：2-10个中文字符
            match = re.search(r'[\u4e00-\u9fa5]{2,10}', text)
            if match:
                name = match.group()
                if re.match(config.VALIDATE_NAME_PATTERN, name):
                    return name

        elif value_type == 'phone':
            # 提取手机号：11位数字
            digits = re.findall(r'\d', text)
            phone = ''.join(digits)
            if len(phone) >= 11:
                phone = phone[:11]  # 取前11位
                if re.match(config.VALIDATE_PHONE_PATTERN, phone):
                    return phone

        elif value_type == 'id_card':
            # 提取身份证号：15位或18位数字/字母
            # 先尝试提取连续的数字和X
            id_match = re.search(r'[0-9X]{15,18}', text.upper())
            if id_match:
                id_card = id_match.group()
                if re.match(config.VALIDATE_ID_CARD_PATTERN, id_card):
                    return id_card

            # 如果没找到，尝试提取所有数字和X，然后组合
            chars = re.findall(r'[0-9X]', text.upper())
            if len(chars) >= 15:
                id_card = ''.join(chars[:18])  # 最多取18位
                if len(id_card) in [15, 18] and re.match(config.VALIDATE_ID_CARD_PATTERN, id_card):
                    return id_card

        return None

    def _extract_by_enhanced_regex(self, text: str) -> List[Dict[str, str]]:
        """
        使用改进的正则表达式进行跨行匹配

        Args:
            text: OCR识别的文本

        Returns:
            提取的人员信息列表
        """
        logger.debug("开始改进的正则表达式跨行匹配")

        # 预处理文本：标准化空白字符，但保留换行信息
        processed_text = re.sub(r'[ \t]+', ' ', text)  # 将空格和制表符标准化为单个空格
        processed_text = re.sub(r'\n+', '\n', processed_text)  # 将多个换行符合并为单个

        # 改进的跨行匹配模式
        enhanced_patterns = {
            'name': [
                r'案例教师姓名[：:\s]*([^\s\n\r]{2,10})',  # 同行匹配
                r'案例教师姓名[：:\s]*\n\s*([^\s\n\r]{2,10})',  # 跨行匹配
                r'姓名[：:\s]*([^\s\n\r]{2,10})',  # 简化标识
                r'姓名[：:\s]*\n\s*([^\s\n\r]{2,10})'  # 简化标识跨行
            ],
            'phone': [
                r'手机号码[：:\s]*(\d{11})',  # 同行匹配
                r'手机号码[：:\s]*\n\s*(\d{11})',  # 跨行匹配
                r'手机号[：:\s]*(\d{11})',  # 简化标识
                r'手机号[：:\s]*\n\s*(\d{11})',  # 简化标识跨行
                r'手机号码[：:\s]*\n\s*(\d{3})\s*(\d{4})\s*(\d{4})',  # 分段匹配
            ],
            'id_card': [
                r'身份证号[：:\s]*([0-9X]{15,18})',  # 同行匹配
                r'身份证号[：:\s]*\n\s*([0-9X]{15,18})',  # 跨行匹配
                r'身份证号[：:\s]*\n\s*([0-9X\s\n]{15,25})',  # 跨行带空格匹配
                r'身份证号[：:\s]*([0-9X\s\n]{15,25})',  # 带空格匹配
            ]
        }

        persons = []
        current_person = {}

        # 对每种信息类型尝试多种模式
        for info_type, patterns in enhanced_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, processed_text, re.IGNORECASE | re.MULTILINE)

                if matches:
                    for match in matches:
                        if isinstance(match, tuple):
                            # 处理分组匹配（如分段的手机号）
                            value = ''.join(match)
                        else:
                            value = match

                        # 清理和验证值
                        cleaned_value = self._clean_extracted_value(value, info_type)
                        if cleaned_value and self._validate_single_field(cleaned_value, info_type):
                            current_person[info_type] = cleaned_value
                            logger.debug(f"正则匹配提取 {info_type}: {cleaned_value}")
                            break  # 找到有效值后跳出模式循环

                    if info_type in current_person:
                        break  # 找到有效值后跳出模式循环

        # 如果收集到足够信息，添加到结果中
        if len(current_person) >= 2:
            if self._validate_person_info(current_person):
                persons.append(current_person)

        return persons

    def _clean_extracted_value(self, value: str, value_type: str) -> str:
        """
        清理提取的值

        Args:
            value: 原始提取值
            value_type: 值类型

        Returns:
            清理后的值
        """
        if not value:
            return ''

        # 移除换行符和多余空格
        cleaned = re.sub(r'\s+', '', value.strip())

        if value_type == 'id_card':
            # 身份证号特殊处理：只保留数字和X
            cleaned = re.sub(r'[^0-9X]', '', cleaned.upper())
        elif value_type == 'phone':
            # 手机号特殊处理：只保留数字
            cleaned = re.sub(r'[^0-9]', '', cleaned)
        elif value_type == 'name':
            # 姓名特殊处理：只保留中文字符
            cleaned = re.sub(r'[^\u4e00-\u9fa5]', '', cleaned)

        return cleaned

    def _validate_single_field(self, value: str, field_type: str) -> bool:
        """
        验证单个字段的有效性

        Args:
            value: 字段值
            field_type: 字段类型

        Returns:
            是否有效
        """
        if field_type == 'name':
            return bool(re.match(config.VALIDATE_NAME_PATTERN, value))
        elif field_type == 'phone':
            return bool(re.match(config.VALIDATE_PHONE_PATTERN, value))
        elif field_type == 'id_card':
            return bool(re.match(config.VALIDATE_ID_CARD_PATTERN, value))

        return False

    def _extract_by_traditional_regex(self, text: str) -> List[Dict[str, str]]:
        """
        使用传统正则表达式匹配（向后兼容）

        Args:
            text: OCR识别的文本

        Returns:
            提取的人员信息列表
        """
        logger.debug("开始传统正则表达式匹配")

        # 清理文本，移除多余的空白字符
        cleaned_text = re.sub(r'\s+', ' ', text.strip())

        # 使用配置的提取规则
        patterns = self.extract_patterns

        # 尝试多种匹配策略
        persons = []

        # 策略1: 直接匹配所有信息
        names = re.findall(patterns['name'], cleaned_text, re.IGNORECASE)
        phones = re.findall(patterns['phone'], cleaned_text, re.IGNORECASE)
        id_cards = re.findall(patterns['id_card'], cleaned_text, re.IGNORECASE)

        logger.debug(f"传统匹配找到姓名: {names}, 手机号: {phones}, 身份证: {id_cards}")

        # 如果三类信息数量一致，按顺序配对
        if len(names) == len(phones) == len(id_cards) and len(names) > 0:
            for i in range(len(names)):
                person = {
                    'name': names[i].strip(),
                    'phone': phones[i].strip(),
                    'id_card': id_cards[i].strip()
                }
                if self._validate_person_info(person):
                    persons.append(person)

        # 策略2: 如果数量不一致，尝试基于位置的匹配
        elif names or phones or id_cards:
            logger.debug("信息数量不一致，尝试基于位置的匹配")

            # 找到所有匹配项及其位置
            all_matches = []

            for match in re.finditer(patterns['name'], cleaned_text, re.IGNORECASE):
                all_matches.append(('name', match.group(1).strip(), match.start()))

            for match in re.finditer(patterns['phone'], cleaned_text, re.IGNORECASE):
                all_matches.append(('phone', match.group(1).strip(), match.start()))

            for match in re.finditer(patterns['id_card'], cleaned_text, re.IGNORECASE):
                all_matches.append(('id_card', match.group(1).strip(), match.start()))

            # 按位置排序
            all_matches.sort(key=lambda x: x[2])

            # 尝试将相邻的信息组合成人员记录
            current_person = {}
            for info_type, value, position in all_matches:
                current_person[info_type] = value

                # 如果收集到完整信息，保存并开始新的记录
                if len(current_person) == 3:
                    if self._validate_person_info(current_person):
                        persons.append(current_person.copy())
                    current_person = {}

            # 处理最后一个不完整的记录
            if len(current_person) >= 2:  # 至少两个字段
                if self._validate_person_info(current_person):
                    persons.append(current_person)

        return persons

        # 尝试多种匹配策略
        persons = []

        # 策略1: 直接匹配所有信息
        names = re.findall(patterns['name'], text, re.IGNORECASE)
        phones = re.findall(patterns['phone'], text, re.IGNORECASE)
        id_cards = re.findall(patterns['id_card'], text, re.IGNORECASE)

        logger.debug(f"找到姓名: {names}, 手机号: {phones}, 身份证: {id_cards}")

        # 如果三类信息数量一致，按顺序配对
        if len(names) == len(phones) == len(id_cards) and len(names) > 0:
            for i in range(len(names)):
                person = {
                    'name': names[i].strip(),
                    'phone': phones[i].strip(),
                    'id_card': id_cards[i].strip()
                }
                if self._validate_person_info(person):
                    persons.append(person)

        # 策略2: 如果数量不一致，尝试基于位置的匹配
        elif names or phones or id_cards:
            logger.warning("信息数量不一致，尝试基于位置的匹配")

            # 找到所有匹配项及其位置
            all_matches = []

            for match in re.finditer(patterns['name'], text, re.IGNORECASE):
                all_matches.append(('name', match.group(1).strip(), match.start()))

            for match in re.finditer(patterns['phone'], text, re.IGNORECASE):
                all_matches.append(('phone', match.group(1).strip(), match.start()))

            for match in re.finditer(patterns['id_card'], text, re.IGNORECASE):
                all_matches.append(('id_card', match.group(1).strip(), match.start()))

            # 按位置排序
            all_matches.sort(key=lambda x: x[2])

            # 尝试将相邻的信息组合成人员记录
            current_person = {}
            for info_type, value, position in all_matches:
                current_person[info_type] = value

                # 如果收集到完整信息，保存并开始新的记录
                if len(current_person) == 3:
                    if self._validate_person_info(current_person):
                        persons.append(current_person.copy())
                    current_person = {}

            # 处理最后一个不完整的记录
            if len(current_person) == 3:
                if self._validate_person_info(current_person):
                    persons.append(current_person)

        logger.info(f"提取到 {len(persons)} 个有效人员信息")
        return persons

    def _validate_person_info(self, person: Dict[str, str]) -> bool:
        """
        验证人员信息的有效性

        Args:
            person: 包含姓名、手机号、身份证号的字典

        Returns:
            信息是否有效
        """
        # 检查必要字段
        if not all(key in person for key in ['name', 'phone', 'id_card']):
            return False

        name = person['name']
        phone = person['phone']
        id_card = person['id_card']

        # 验证姓名（2-10个中文字符）
        if not re.match(config.VALIDATE_NAME_PATTERN, name):
            logger.warning(f"无效姓名: {name}")
            return False

        # 验证手机号（11位数字）
        if not re.match(config.VALIDATE_PHONE_PATTERN, phone):
            logger.warning(f"无效手机号: {phone}")
            return False

        # 验证身份证号（15位或18位）
        if not re.match(config.VALIDATE_ID_CARD_PATTERN, id_card.upper()):
            logger.warning(f"无效身份证号: {id_card}")
            return False

        return True

    def process_pdf_url(self, pdf_url: str) -> List[Dict[str, str]]:
        """
        处理单个PDF URL，返回提取的人员信息

        Args:
            pdf_url: PDF文件的下载链接

        Returns:
            人员信息列表
        """
        try:
            logger.info(f"开始处理PDF: {pdf_url}")

            # 下载PDF
            pdf_content = self.download_pdf(pdf_url)

            # 转换为图片
            images = self.pdf_to_images(pdf_content)

            # 对每页进行OCR识别
            all_persons = []
            for page_num, image_data in enumerate(images):
                logger.info(f"处理第 {page_num + 1} 页")

                # 转换为base64
                base64_image = self.image_to_base64(image_data)

                # OCR识别（获取结构化数据）
                ocr_result = self.call_ocr_api(base64_image)

                # 提取人员信息
                persons = self.extract_person_info(ocr_result)
                all_persons.extend(persons)

            # 去重（基于身份证号）
            unique_persons = []
            seen_ids = set()
            for person in all_persons:
                if person['id_card'] not in seen_ids:
                    unique_persons.append(person)
                    seen_ids.add(person['id_card'])

            logger.info(f"PDF处理完成，提取到 {len(unique_persons)} 个唯一人员信息")
            return unique_persons

        except Exception as e:
            logger.error(f"处理PDF失败: {pdf_url}, 错误: {str(e)}")
            return []


class ExcelProcessor:
    """Excel文件处理器"""

    def __init__(self, excel_path: str):
        """
        初始化Excel处理器

        Args:
            excel_path: Excel文件路径
        """
        self.excel_path = excel_path
        self.ocr_processor = OCRProcessor()

    def load_excel(self) -> pd.DataFrame:
        """
        加载Excel文件

        Returns:
            pandas DataFrame

        Raises:
            Exception: 文件加载失败时抛出异常
        """
        try:
            logger.info(f"加载Excel文件: {self.excel_path}")

            # 尝试不同的读取方式
            try:
                df = pd.read_excel(self.excel_path, engine='openpyxl')
            except Exception:
                df = pd.read_excel(self.excel_path, engine='xlrd')

            logger.info(f"Excel加载成功，共 {len(df)} 行数据")
            return df

        except Exception as e:
            logger.error(f"Excel文件加载失败: {str(e)}")
            raise

    def find_url_column(self, df: pd.DataFrame) -> str:
        """
        查找包含PDF URL的列

        Args:
            df: pandas DataFrame

        Returns:
            列名

        Raises:
            ValueError: 找不到URL列时抛出异常
        """
        # 按优先级查找URL列
        for col_name in config.EXCEL_URL_COLUMN_NAMES:
            if col_name in df.columns:
                logger.info(f"找到URL列: {col_name}")
                return col_name

        # 查找包含 'url' 的列名
        url_columns = [col for col in df.columns if 'url' in col.lower()]
        if url_columns:
            logger.info(f"找到URL列: {url_columns[0]}")
            return url_columns[0]

        # 查找包含HTTP链接的列
        for col in df.columns:
            if df[col].dtype == 'object':  # 字符串类型
                sample_values = df[col].dropna().head(10)
                if any(str(val).startswith(('http://', 'https://')) for val in sample_values):
                    logger.info(f"根据内容识别URL列: {col}")
                    return col

        raise ValueError("未找到包含PDF URL的列，请确保Excel文件包含 'tjb_url' 列或其他URL列")

    def process_excel(self) -> pd.DataFrame:
        """
        处理Excel文件，对每行的PDF进行OCR识别

        Returns:
            包含处理结果的DataFrame
        """
        try:
            # 加载Excel
            df = self.load_excel()

            # 查找URL列
            url_column = self.find_url_column(df)

            # 添加结果列
            df['ocr_persons'] = None
            df['person_count'] = 0
            df['processing_status'] = 'pending'
            df['error_message'] = ''

            # 处理每一行
            total_rows = len(df)
            for index, row in df.iterrows():
                logger.info(f"处理第 {index + 1}/{total_rows} 行")

                pdf_url = row[url_column]
                if pd.isna(pdf_url) or not str(pdf_url).strip():
                    df.at[index, 'processing_status'] = 'skipped'
                    df.at[index, 'error_message'] = 'URL为空'
                    continue

                try:
                    # 处理PDF
                    persons = self.ocr_processor.process_pdf_url(str(pdf_url).strip())

                    # 保存结果
                    df.at[index, 'ocr_persons'] = persons
                    df.at[index, 'person_count'] = len(persons)
                    df.at[index, 'processing_status'] = 'success'

                    logger.info(f"第 {index + 1} 行处理成功，提取到 {len(persons)} 个人员信息")

                except Exception as e:
                    error_msg = str(e)
                    df.at[index, 'processing_status'] = 'failed'
                    df.at[index, 'error_message'] = error_msg
                    logger.error(f"第 {index + 1} 行处理失败: {error_msg}")

            return df

        except Exception as e:
            logger.error(f"Excel处理失败: {str(e)}")
            raise

    def save_results(self, df: pd.DataFrame, output_path: str):
        """
        保存处理结果

        Args:
            df: 包含处理结果的DataFrame
            output_path: 输出文件路径
        """
        try:
            logger.info(f"保存结果到: {output_path}")

            # 创建详细结果DataFrame
            detailed_results = []

            for index, row in df.iterrows():
                base_info = {
                    'original_row': index + 1,
                    'pdf_url': row.get('tjb_url', ''),
                    'processing_status': row.get('processing_status', ''),
                    'error_message': row.get('error_message', ''),
                    'person_count': row.get('person_count', 0)
                }

                persons = row.get('ocr_persons', [])
                if persons:
                    for person_index, person in enumerate(persons):
                        result_row = base_info.copy()
                        result_row.update({
                            'person_index': person_index + 1,
                            'name': person.get('name', ''),
                            'phone': person.get('phone', ''),
                            'id_card': person.get('id_card', '')
                        })
                        detailed_results.append(result_row)
                else:
                    # 没有提取到人员信息的行也要保留
                    result_row = base_info.copy()
                    result_row.update({
                        'person_index': 0,
                        'name': '',
                        'phone': '',
                        'id_card': ''
                    })
                    detailed_results.append(result_row)

            # 保存详细结果
            result_df = pd.DataFrame(detailed_results)
            result_df.to_excel(output_path, index=False, engine='openpyxl')

            # 生成统计报告
            stats = {
                'total_rows': len(df),
                'success_rows': len(df[df['processing_status'] == 'success']),
                'failed_rows': len(df[df['processing_status'] == 'failed']),
                'skipped_rows': len(df[df['processing_status'] == 'skipped']),
                'total_persons': df['person_count'].sum()
            }

            logger.info("处理统计:")
            logger.info(f"  总行数: {stats['total_rows']}")
            logger.info(f"  成功: {stats['success_rows']}")
            logger.info(f"  失败: {stats['failed_rows']}")
            logger.info(f"  跳过: {stats['skipped_rows']}")
            logger.info(f"  提取人员总数: {stats['total_persons']}")

            # 保存统计报告
            stats_path = output_path.replace('.xlsx', '_stats.txt')
            with open(stats_path, 'w', encoding='utf-8') as f:
                f.write("OCR处理统计报告\n")
                f.write("=" * 30 + "\n")
                f.write(f"总行数: {stats['total_rows']}\n")
                f.write(f"成功处理: {stats['success_rows']}\n")
                f.write(f"处理失败: {stats['failed_rows']}\n")
                f.write(f"跳过处理: {stats['skipped_rows']}\n")
                f.write(f"提取人员总数: {stats['total_persons']}\n")
                f.write(f"成功率: {stats['success_rows']/stats['total_rows']*100:.1f}%\n")

            logger.info(f"结果保存完成: {output_path}")
            logger.info(f"统计报告保存: {stats_path}")

        except Exception as e:
            logger.error(f"保存结果失败: {str(e)}")
            raise


def main(excel_path: str = None, output_path: str = None):
    """
    主函数

    Args:
        excel_path: Excel文件路径，如果为None则使用默认路径
        output_path: 输出文件路径，如果为None则自动生成
    """
    try:
        # 设置默认路径
        if excel_path is None:
            excel_path = input("请输入Excel文件路径: ").strip()

        if not os.path.exists(excel_path):
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")

        if output_path is None:
            # 自动生成输出路径
            base_name = os.path.splitext(os.path.basename(excel_path))[0]
            output_dir = os.path.dirname(excel_path)
            output_path = os.path.join(output_dir, f"{base_name}_ocr_results.xlsx")

        logger.info("开始处理Excel文件")
        logger.info(f"输入文件: {excel_path}")
        logger.info(f"输出文件: {output_path}")

        # 创建处理器并执行处理
        processor = ExcelProcessor(excel_path)
        result_df = processor.process_excel()
        processor.save_results(result_df, output_path)

        logger.info("处理完成!")

    except KeyboardInterrupt:
        logger.info("用户中断处理")
    except Exception as e:
        logger.error(f"处理失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()

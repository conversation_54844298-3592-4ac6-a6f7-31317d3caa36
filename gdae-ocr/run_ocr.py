#!/usr/bin/env python3
"""
OCR处理系统启动脚本
"""

import os
import sys
import argparse
import importlib.util
from pathlib import Path


def load_ocr_module():
    """动态加载OCR模块"""
    module_path = Path(__file__).parent / "ocr" / "2025_zxxzhjy.py"
    spec = importlib.util.spec_from_file_location("zxxzhjy_module", module_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='广东省智慧教育平台OCR识别系统')
    parser.add_argument('excel_file', nargs='?', help='Excel文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    
    args = parser.parse_args()
    
    if args.test:
        # 运行测试
        print("运行测试模式...")
        import test_ocr
        test_ocr.main_test()
        return
    
    # 加载OCR模块
    ocr_module = load_ocr_module()
    
    if args.excel_file:
        # 使用命令行参数
        if not os.path.exists(args.excel_file):
            print(f"错误: Excel文件不存在: {args.excel_file}")
            sys.exit(1)
        
        print(f"处理Excel文件: {args.excel_file}")
        ocr_module.main(args.excel_file, args.output)
    else:
        # 交互模式
        ocr_module.main()


if __name__ == "__main__":
    main()

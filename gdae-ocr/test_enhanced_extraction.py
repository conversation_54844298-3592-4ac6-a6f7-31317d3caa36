#!/usr/bin/env python3
"""
测试增强的信息提取功能
"""

import os
import sys
import json
import importlib.util
from pathlib import Path

# 动态加载OCR模块
def load_ocr_module():
    module_path = Path(__file__).parent / "ocr" / "2025_zxxzhjy.py"
    spec = importlib.util.spec_from_file_location("zxxzhjy_module", module_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def test_structured_data_extraction():
    """测试基于结构化数据的信息提取"""
    print("=== 测试基于结构化数据的信息提取 ===")
    
    # 模拟OCR API返回的结构化数据
    mock_structured_data = [
        {
            "text": "案例教师姓名",
            "score": 0.99,
            "box": [[100, 100], [200, 100], [200, 120], [100, 120]],
            "end": ""
        },
        {
            "text": "张三",
            "score": 0.97,
            "box": [[220, 100], [260, 100], [260, 120], [220, 120]],
            "end": "\n"
        },
        {
            "text": "手机号码",
            "score": 0.98,
            "box": [[100, 140], [180, 140], [180, 160], [100, 160]],
            "end": ""
        },
        {
            "text": "13800138000",
            "score": 0.96,
            "box": [[200, 140], [320, 140], [320, 160], [200, 160]],
            "end": "\n"
        },
        {
            "text": "身份证号",
            "score": 0.99,
            "box": [[100, 180], [170, 180], [170, 200], [100, 200]],
            "end": ""
        },
        {
            "text": "110101199001",
            "score": 0.95,
            "box": [[190, 180], [310, 180], [310, 200], [190, 200]],
            "end": ""
        },
        {
            "text": "011234",
            "score": 0.94,
            "box": [[190, 200], [250, 200], [250, 220], [190, 220]],
            "end": "\n"
        }
    ]
    
    # 创建OCR结果对象
    ocr_result = {
        'text': '案例教师姓名张三\n手机号码13800138000\n身份证号110101199001011234\n',
        'structured_data': mock_structured_data,
        'success': True
    }
    
    # 加载OCR模块并测试
    ocr_module = load_ocr_module()
    processor = ocr_module.OCRProcessor()
    
    persons = processor.extract_person_info(ocr_result)
    
    print(f"提取结果: {persons}")
    
    if persons:
        person = persons[0]
        print(f"姓名: {person.get('name', 'N/A')}")
        print(f"手机号: {person.get('phone', 'N/A')}")
        print(f"身份证号: {person.get('id_card', 'N/A')}")
        print("✅ 结构化数据提取测试通过")
    else:
        print("❌ 结构化数据提取测试失败")


def test_cross_line_extraction():
    """测试跨行文本提取"""
    print("\n=== 测试跨行文本提取 ===")
    
    # 模拟跨行的OCR文本
    cross_line_text = """
    案例教师姓名
    李四
    手机号码
    13900139000
    身份证号
    220101199002
    022345
    """
    
    ocr_module = load_ocr_module()
    processor = ocr_module.OCRProcessor()
    
    # 测试改进的正则表达式匹配
    persons = processor._extract_by_enhanced_regex(cross_line_text)
    
    print(f"跨行提取结果: {persons}")
    
    if persons:
        person = persons[0]
        print(f"姓名: {person.get('name', 'N/A')}")
        print(f"手机号: {person.get('phone', 'N/A')}")
        print(f"身份证号: {person.get('id_card', 'N/A')}")
        print("✅ 跨行文本提取测试通过")
    else:
        print("❌ 跨行文本提取测试失败")


def test_fragmented_id_card():
    """测试分段身份证号提取"""
    print("\n=== 测试分段身份证号提取 ===")
    
    # 模拟分段的身份证号
    fragmented_text = """
    案例教师姓名: 王五
    手机号码: 13700137000
    身份证号: 330101 1990 03 03 3456
    """
    
    ocr_module = load_ocr_module()
    processor = ocr_module.OCRProcessor()
    
    persons = processor._extract_by_enhanced_regex(fragmented_text)
    
    print(f"分段提取结果: {persons}")
    
    if persons:
        person = persons[0]
        print(f"姓名: {person.get('name', 'N/A')}")
        print(f"手机号: {person.get('phone', 'N/A')}")
        print(f"身份证号: {person.get('id_card', 'N/A')}")
        print("✅ 分段身份证号提取测试通过")
    else:
        print("❌ 分段身份证号提取测试失败")


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n=== 测试向后兼容性 ===")
    
    # 使用传统的纯文本输入
    traditional_text = "案例教师姓名赵六 手机号码13600136000 身份证号******************"
    
    ocr_module = load_ocr_module()
    processor = ocr_module.OCRProcessor()
    
    # 直接传入字符串（向后兼容）
    persons = processor.extract_person_info(traditional_text)
    
    print(f"向后兼容提取结果: {persons}")
    
    if persons:
        person = persons[0]
        print(f"姓名: {person.get('name', 'N/A')}")
        print(f"手机号: {person.get('phone', 'N/A')}")
        print(f"身份证号: {person.get('id_card', 'N/A')}")
        print("✅ 向后兼容性测试通过")
    else:
        print("❌ 向后兼容性测试失败")


def test_table_format_extraction():
    """测试表格格式文本提取"""
    print("\n=== 测试表格格式文本提取 ===")
    
    # 模拟表格格式的OCR文本
    table_text = """
    |案例教师姓名  | 孙七        |
    |手机号码      | 13500135000 |
    |身份证号      | 550101      |
    |              | 199005      |
    |              | 055678      |
    """
    
    ocr_module = load_ocr_module()
    processor = ocr_module.OCRProcessor()
    
    persons = processor._extract_by_enhanced_regex(table_text)
    
    print(f"表格格式提取结果: {persons}")
    
    if persons:
        person = persons[0]
        print(f"姓名: {person.get('name', 'N/A')}")
        print(f"手机号: {person.get('phone', 'N/A')}")
        print(f"身份证号: {person.get('id_card', 'N/A')}")
        print("✅ 表格格式提取测试通过")
    else:
        print("❌ 表格格式提取测试失败")


def main():
    """主测试函数"""
    print("增强信息提取功能测试")
    print("=" * 50)
    
    try:
        test_structured_data_extraction()
        test_cross_line_extraction()
        test_fragmented_id_card()
        test_backward_compatibility()
        test_table_format_extraction()
        
        print("\n" + "=" * 50)
        print("所有测试完成!")
        print("注意: 实际使用时需要确保OCR服务返回正确的结构化数据格式")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

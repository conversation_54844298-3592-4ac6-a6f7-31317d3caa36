#!/usr/bin/env python3
"""
测试OCR功能的简单脚本
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 动态导入模块（因为文件名以数字开头）
import importlib.util
spec = importlib.util.spec_from_file_location("zxxzhjy_module", "ocr/2025_zxxzhjy.py")
zxxzhjy_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(zxxzhjy_module)

# 从模块中导入需要的类和函数
main = zxxzhjy_module.main
ExcelProcessor = zxxzhjy_module.ExcelProcessor
OCRProcessor = zxxzhjy_module.OCRProcessor


def create_test_excel():
    """创建测试用的Excel文件"""
    test_data = {
        'tjb_url': [
            'https://example.com/test1.pdf',  # 这里需要替换为真实的PDF URL
            'https://example.com/test2.pdf',  # 这里需要替换为真实的PDF URL
            '',  # 空URL测试
        ],
        'other_column': ['数据1', '数据2', '数据3']
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_input.xlsx'
    df.to_excel(test_file, index=False)
    print(f"创建测试Excel文件: {test_file}")
    return test_file


def test_ocr_processor():
    """测试OCR处理器的基本功能"""
    print("测试OCR处理器...")
    
    processor = OCRProcessor()
    
    # 测试OCR API连接
    try:
        # 创建一个简单的测试图片（白色背景，黑色文字）
        from PIL import Image, ImageDraw, ImageFont
        import base64
        from io import BytesIO
        
        # 创建测试图片
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # 添加测试文字
        test_text = "案例教师姓名 张三\n手机号码 13800138000\n身份证号 110101199001011234"
        draw.text((10, 10), test_text, fill='black')
        
        # 转换为base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        base64_image = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        # 测试OCR API
        result = processor.call_ocr_api(base64_image)
        print(f"OCR测试结果: {result[:100]}...")
        
        # 测试信息提取
        persons = processor.extract_person_info(result)
        print(f"提取到的人员信息: {persons}")
        
    except Exception as e:
        print(f"OCR测试失败: {e}")
        print("请确保OCR服务正在运行在 http://127.0.0.1:1224/api/ocr")


def test_excel_processing():
    """测试Excel处理功能"""
    print("\n测试Excel处理功能...")
    
    # 创建测试Excel文件
    test_file = create_test_excel()
    
    try:
        # 处理Excel文件
        processor = ExcelProcessor(test_file)
        
        # 加载Excel
        df = processor.load_excel()
        print(f"Excel加载成功，共 {len(df)} 行")
        
        # 查找URL列
        url_column = processor.find_url_column(df)
        print(f"找到URL列: {url_column}")
        
        print("注意: 实际的PDF处理需要有效的PDF URL")
        
    except Exception as e:
        print(f"Excel处理测试失败: {e}")
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"清理测试文件: {test_file}")


def main_test():
    """主测试函数"""
    print("开始OCR功能测试")
    print("=" * 50)
    
    # 测试OCR处理器
    test_ocr_processor()
    
    # 测试Excel处理
    test_excel_processing()
    
    print("\n测试完成!")
    print("=" * 50)
    print("使用说明:")
    print("1. 确保OCR服务运行在 http://127.0.0.1:1224/api/ocr")
    print("2. 准备包含 'tjb_url' 列的Excel文件")
    print("3. 运行: python -m ocr.2025_zxxzhjy")


if __name__ == "__main__":
    main_test()

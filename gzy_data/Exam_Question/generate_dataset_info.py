import os
import json
import glob

def generate_dataset_info(root_dir):
    names = []
    dataset_dict = {}

    # 四个主类别和三个子类别
    main_categories = ["成果", "需求", "专家", "专利"]
    sub_categories = ["问答对", "语料库", "摘要"]

    for main in main_categories:
        for sub in sub_categories:
            folder_path = os.path.join(root_dir, main, sub)
            if not os.path.isdir(folder_path):
                continue  # 跳过不存在的目录

            json_files = glob.glob(os.path.join(folder_path, "*.json"))

            for file_path in json_files:
                full_name = os.path.basename(file_path)
                original_base = os.path.splitext(full_name)[0]

                # 构造新的 base_name: 主_子_原始文件名
                new_base_name = f"{main}_{sub}_{original_base}"
                # 构造新的 file_name: sft/主/子/原始文件名.json
                new_file_name = f"sft/{main}/{sub}/{full_name}"

                names.append(new_base_name)

                dataset_dict[new_base_name] = {
                    "file_name": new_file_name,
                    "formatting": "alpaca",
                    "columns": {
                        "prompt": "instruction",
                        "response": "output"
                    }
                }

    json_str = json.dumps(dataset_dict, indent=2, ensure_ascii=False)
    return ",".join(names), json_str


if __name__ == "__main__":
    root_directory = r"C:\Users\<USER>\scnunc\软件开发与运营中心 - 语料库"
    names_str, json_str = generate_dataset_info(root_directory)

    print("文件名字符串（无后缀，逗号分隔）:")
    print(names_str)
    print("\nJSON 字符串:")
    print(json_str)
import re
import json
import pandas as pd

# 优化后的正则表达式，匹配章节标题和内容
re_chapter = re.compile(r"第\s*([一二三四五六七八九十]+\s*)+章\s*(.*?)(?=\n\s*第\s*([一二三四五六七八九十]+\s*)+章|\Z)",
                        re.DOTALL)

# 匹配大类型题目（选择题、判断题等）
re_big_type = re.compile(
    r"([一二三四五六七八九十]+)\s*(选择题|判断题|填空题|问答题|名词解释|是非判断分析题|分析题|简答题|论述题|是非判断题)\s*(.*?)(?=\s*[一二三四五六七八九十]+\s*(选择题|判断题|填空题|问答题|名词解释|是非判断分析题|分析题|简答题|论述题|是非判断题)|\n\s*参考答案|\Z)",
    re.DOTALL)
# 匹配小类型题目（单选题、多选题、配伍选择题）
re_small_type = re.compile(
    r"（([一二三四五六七八九十]+)）\s*(A型题|A1型题|A2型题|A3型题|X型题|B型题|单选题|多选题|多项选择题|配伍选择题)\s*(.*?)(?=\s*（[一二三四五六七八九十]+）\s*(A型题|A1型题|A2型题|A3型题|X型题|B型题|单选题|多选题|配伍选择题)|\n\s*参考答案|\Z)",
    re.DOTALL)
# 匹配问题及选项，确保以数字开头并跟随一个点或中文句号
re_question = re.compile(r"(\d+)\s*[\.\、]\s*(.*?)(?=\s*\d+\s*[\.\、]|\Z)", re.DOTALL)

# 匹配参考答案部分
re_answer = re.compile(r"参考答案\s*(.*?)$", re.DOTALL)


def parse_file(file_path):
    data = []

    with open(file_path, 'r', encoding='utf-8') as file:
        text = file.read().replace('(', '（').replace(')', '）')

        # 匹配所有章节
        matches = re_chapter.findall(text)

        # 用于去重章节
        processed_chapters = set()

        for chapter in matches:
            # 章节标题，去掉空格
            title = ''.join(chapter[0].strip().split())
            # 章节内容
            content = chapter[1].strip()

            if title in processed_chapters:
                continue  # 跳过已经处理过的章节

            processed_chapters.add(title)

            questions = []
            answers_dict = {}

            # 分离参考答案部分
            answers_match = re_answer.search(content)
            if answers_match:
                answer_text = answers_match.group(1).strip()
                # 移除参考答案部分
                content = re_answer.sub('', content).strip()

            # 匹配大类型题目（选择题、判断题等）
            big_types = re_big_type.findall(content)
            print(f"Big types found in {title}: {big_types}")

            for big_type in big_types:
                try:
                    # 调整解包逻辑，处理可能的额外元素
                    if len(big_type) == 3:
                        big_type_number, big_type_name, content_section = big_type
                    else:
                        big_type_number = big_type[0]
                        big_type_name = big_type[1]
                        # 将剩余部分合并为一个字符串
                        content_section = ''.join(big_type[2:3])

                    current_big_type = big_type_name.strip()

                    # 处理选择题类
                    if current_big_type == '选择题':
                        small_types = re_small_type.findall(content_section)
                        print(f"Small types found in {title}_{big_type_number}: {small_types}")

                        for small_type in small_types:
                            try:
                                # 调整解包逻辑，处理可能的额外元素
                                if len(small_type) == 3:
                                    small_type_number, small_type_name, small_content = small_type
                                else:
                                    small_type_number = small_type[0]
                                    small_type_name = small_type[1]
                                    # 将剩余部分合并为一个字符串
                                    small_content = ''.join(small_type[2:3])

                                current_small_type = small_type_name.strip()
                                if current_small_type == '配伍选择题' or current_small_type == 'B型题':
                                    continue
                                if current_small_type == 'A型题' or current_small_type == 'A1型题' or current_small_type == 'A2型题' or current_small_type == 'A3型题' or current_small_type == 'X型题':
                                    current_small_type = '单选题'
                                if current_small_type == '多项选择题':
                                    current_small_type = '多选题'
                                parsed_questions = process_choice_questions(small_content, current_small_type,
                                                                            f"{title}_{big_type_number}_{small_type_number}")
                                questions.extend(parsed_questions)
                                answers_dict.update({q['question_id']: q for q in parsed_questions})

                            except ValueError as e:
                                print(
                                    f"Error small type in {title}_{big_type_number}: {small_type}, Error: {e}")

                    # 处理判断题、填空题、问答题、名词解释
                    elif current_big_type == '判断题' or current_big_type == '是非判断分析题' or current_big_type == '是非判断题':
                        parsed_questions = process_true_false_questions(content_section, f"{title}_{big_type_number}")
                        questions.extend(parsed_questions)
                        answers_dict.update({q['question_id']: q for q in parsed_questions})
                    elif current_big_type == '填空题':
                        parsed_questions = process_fill_in_the_blank_questions(content_section,
                                                                               f"{title}_{big_type_number}")
                        questions.extend(parsed_questions)
                        answers_dict.update({q['question_id']: q for q in parsed_questions})
                    elif current_big_type == '问答题' or current_big_type == '分析题' or current_big_type == '简答题' or current_big_type == '论述题':
                        parsed_questions = process_short_answer_questions(content_section, f"{title}_{big_type_number}",
                                                                          current_big_type)
                        questions.extend(parsed_questions)
                        answers_dict.update({q['question_id']: q for q in parsed_questions})
                    elif current_big_type == '名词解释':
                        parsed_questions = process_term_explanation_questions(content_section,
                                                                              f"{title}_{big_type_number}")
                        questions.extend(parsed_questions)
                        answers_dict.update({q['question_id']: q for q in parsed_questions})

                except ValueError as e:
                    print(f"Error big type in {title}: {big_type}, Error: {e}")

            # 匹配参考答案
            if answers_match:
                big_types = re_big_type.findall(answer_text)
                print(f"Big types found in {title}: {big_types}")

                for big_type in big_types:
                    try:
                        # 调整解包逻辑，处理可能的额外元素
                        if len(big_type) == 3:
                            big_type_number, big_type_name, content_section = big_type
                        else:
                            big_type_number = big_type[0]
                            big_type_name = big_type[1]
                            # 将剩余部分合并为一个字符串
                            content_section = ''.join(big_type[2:3])

                        current_big_type = big_type_name.strip()

                        # 处理选择题类
                        if current_big_type == '选择题':
                            small_types = re_small_type.findall(content_section)
                            print(f"Small types found in {title}_{big_type_number}: {small_types}")

                            for small_type in small_types:
                                try:
                                    # 调整解包逻辑，处理可能的额外元素
                                    if len(small_type) == 3:
                                        small_type_number, small_type_name, small_content = small_type
                                    else:
                                        small_type_number = small_type[0]
                                        small_type_name = small_type[1]
                                        # 将剩余部分合并为一个字符串
                                        small_content = ''.join(small_type[2:3])

                                    current_small_type = small_type_name.strip()
                                    if current_small_type == '配伍选择题':
                                        continue
                                    answers = process_choice_questions_answer(small_content,
                                                                              f"{title}_{big_type_number}_{small_type_number}")
                                    for answer in answers:
                                        if answer['question_id'] in answers_dict:
                                            answers_dict[answer['question_id']]['answer'] = answer['answer']

                                except ValueError as e:
                                    print(
                                        f"Error unpacking small type in {title}_{big_type_number}: {small_type}, Error: {e}")

                        # 处理判断题、填空题、问答题、名词解释
                        elif current_big_type == '判断题' or current_big_type == '是非判断分析题' or current_big_type == '是非判断题':
                            answers = process_true_false_questions_answer(content_section, f"{title}_{big_type_number}",
                                                                          current_big_type)
                            for answer in answers:
                                if answer['question_id'] in answers_dict:
                                    answers_dict[answer['question_id']]['answer'] = answer['answer']
                                    answers_dict[answer['question_id']]['explain'] = answer['explain']
                        elif current_big_type == '填空题':
                            answers = process_fill_in_the_blank_questions_answer(content_section,
                                                                                 f"{title}_{big_type_number}")
                            for answer in answers:
                                if answer['question_id'] in answers_dict:
                                    answers_dict[answer['question_id']]['answer'] = answer['answer']

                        elif current_big_type == '问答题' or current_big_type == '分析题' or current_big_type == '简答题' or current_big_type == '论述题':
                            answers = process_short_answer_questions_answer(content_section,
                                                                            f"{title}_{big_type_number}")
                            for answer in answers:
                                if answer['question_id'] in answers_dict:
                                    answers_dict[answer['question_id']]['answer'] = answer['answer']

                        elif current_big_type == '名词解释':
                            answers = process_term_explanation_questions_answer(content_section,
                                                                                f"{title}_{big_type_number}")
                            for answer in answers:
                                if answer['question_id'] in answers_dict:
                                    answers_dict[answer['question_id']]['answer'] = answer['answer']


                    except ValueError as e:
                        print(f"Error big type in {title}: {big_type}, Error: {e}")

            # 添加章节和题目到最终数据中
            data = data + list(answers_dict.values())

    return data


def process_choice_questions(content, q_type, base_id):
    # 处理选择题（单选题/多选题）
    questions = []
    question_items = re_question.findall(content)
    print(f"Questions found in {base_id}: {question_items}")

    for idx, question in enumerate(question_items, start=1):
        # 提取题目序号
        q_number = question[0].strip()
        # 提取题目内容
        q_content = question[1].strip()
        question_id = f"{base_id}_{q_number}"
        questions.append({
            "question_id": question_id,
            "question_number": q_number,
            "content": q_content.replace(" ", ""),
            "answer": "",
            "type": q_type
        })
    return questions


def process_true_false_questions(content, base_id):
    # 处理判断题
    questions = []
    question_items = re_question.findall(content)
    for idx, question in enumerate(question_items, start=1):
        # 提取题目序号
        q_number = question[0].strip()
        # 提取题目内容
        q_content = question[1].strip()
        question_id = f"{base_id}_{q_number}"
        questions.append({
            "question_id": question_id,
            "question_number": q_number,
            "content": q_content.replace(" ", ""),
            "answer": "",
            "type": "判断题"
        })
    return questions


def process_fill_in_the_blank_questions(content, base_id):
    # 处理填空题
    questions = []
    question_items = re_question.findall(content)
    for idx, question in enumerate(question_items, start=1):
        # 提取题目序号
        q_number = question[0].strip()
        # 提取题目内容
        q_content = question[1].strip()
        question_id = f"{base_id}_{q_number}"
        questions.append({
            "question_id": question_id,
            "question_number": q_number,
            "content": q_content,
            "answer": "",
            "type": "填空题"
        })
    return questions


def process_short_answer_questions(content, base_id, current_big_type):
    # 处理问答题
    questions = []
    question_items = re_question.findall(content)
    for idx, question in enumerate(question_items, start=1):
        # 提取题目序号
        q_number = question[0].strip()
        # 提取题目内容
        q_content = question[1].strip()
        question_id = f"{base_id}_{q_number}"
        questions.append({
            "question_id": question_id,
            "question_number": q_number,
            "content": q_content.replace(" ", ""),
            "answer": "",
            "type": current_big_type
        })
    return questions


def process_term_explanation_questions(content, base_id):
    # 处理名词解释题
    questions = []
    question_items = re_question.findall(content)
    for idx, question in enumerate(question_items, start=1):
        # 提取题目序号
        q_number = question[0].strip()
        # 提取题目内容
        q_content = question[1].strip()
        question_id = f"{base_id}_{q_number}"
        questions.append({
            "question_id": question_id,
            "question_number": q_number,
            "content": q_content.replace(" ", ""),
            "answer": "",
            "type": "名词解释"
        })
    return questions


def process_choice_questions_answer(content, base_id):
    # 处理选择题（单选题/多选题）
    answers = []
    question_items = re_question.findall(content)
    print(f"Questions found in {base_id}: {question_items}")

    for idx, question in enumerate(question_items, start=1):
        # 提取答案序号
        q_number = question[0].strip()
        # 提取答案内容
        q_content = question[1].strip()
        question_id = f"{base_id}_{q_number}"
        answers.append({
            "question_id": question_id,
            "answer": q_content.replace(" ", ""),
        })
    return answers


def process_true_false_questions_answer(content, base_id, current_big_type):
    # 处理判断题
    answers = []
    question_items = re_question.findall(content)
    for idx, question in enumerate(question_items, start=1):
        # 提取答案序号
        q_number = question[0].strip()
        # 提取答案内容
        q_content = question[1].strip()
        answer = q_content.replace(" ", "")
        explain = ""
        question_id = f"{base_id}_{q_number}"
        if current_big_type == '是非判断分析题' or current_big_type=='是非判断题':
            match = re.search(r"(?:答：\s*)?(正确|错误)\s*。?\s*(.*)", q_content)
            if match:
                answer = match.group(1)
                explain = match.group(2)
            else:
                # 如果没有“答：”，直接匹配“正确。”或“正确。你好啊”
                match = re.search(r"(正确|错误)\s*。?\s*(.*)", q_content)
                if match:
                    answer = match.group(1)
                    explain = match.group(2)
        answers.append({
            "question_id": question_id,
            "answer": answer,
            "explain": explain
        })
    return answers


def process_fill_in_the_blank_questions_answer(content, base_id):
    # 处理填空题
    answers = []
    question_items = re_question.findall(content)
    for idx, question in enumerate(question_items, start=1):
        # 提取答案序号
        q_number = question[0].strip()
        # 提取答案内容
        q_content = question[1].strip()
        question_id = f"{base_id}_{q_number}"
        answers.append({
            "question_id": question_id,
            "answer": q_content,
        })
    return answers


def process_short_answer_questions_answer(content, base_id):
    # 处理问答题
    answers = []
    question_items = re_question.findall(content)
    for idx, question in enumerate(question_items, start=1):
        # 提取答案序号
        q_number = question[0].strip()
        # 提取答案内容
        q_content = question[1].strip()
        question_id = f"{base_id}_{q_number}"
        answers.append({
            "question_id": question_id,
            "answer": q_content.replace(" ", ""),
        })
    return answers


def process_term_explanation_questions_answer(content, base_id):
    # 处理名词解释题
    answers = []
    question_items = re_question.findall(content)
    for idx, question in enumerate(question_items, start=1):
        # 提取答案序号
        q_number = question[0].strip()
        # 提取答案内容
        q_content = question[1].strip()
        question_id = f"{base_id}_{q_number}"
        answers.append({
            "question_id": question_id,
            "answer": q_content.replace(" ", ""),
        })
    return answers


def save_to_json(data, output_file):
    with open(output_file, 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=4)


def save_to_excel(data, output_file):
    # 创建一个空的 DataFrame 列表
    dfs = []

    # 遍历每个问题
    for item in data:
        # 提取所需字段
        content = item.get('content') or '无'  # 题目
        content = content.replace('\n', '') if content else '无'  # 去除换行符并处理为空的情况

        answer = item.get('answer') or '无'  # 答案
        question_type = item.get('type') or '无'  # 类型
        explain = item.get('explain') or '无'  # 解释

        if content == '无' or answer == '无' or question_type == '无':
            continue

        # 创建一个字典以匹配列
        row = {
            '题目': content,
            '答案': answer,
            '类型': question_type,
            '解析': explain,
            'id': item.get('question_id')
        }

        # 将字典添加到 DataFrame 列表中
        dfs.append(row)

    # 创建 DataFrame
    df = pd.DataFrame(dfs, columns=['题目', '答案', '类型', '解析','id'])

    # 保存到 Excel 文件
    df.to_excel(output_file, index=False, engine='openpyxl')


name = '中医儿科学习题集'
file_path = f'txt/{name}.txt'
output_file = 'output.json'
output_excel = f'excel/{name}.xlsx'
data = parse_file(file_path)
save_to_json(data, output_file)
save_to_excel(data, output_excel)

print(f"数据已保存到 {output_file}")

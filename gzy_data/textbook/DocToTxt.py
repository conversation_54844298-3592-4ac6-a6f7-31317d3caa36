import zipfile
import xml.etree.ElementTree as ET

def extract_all_text_from_docx(docx_filename):
    """
    提取docx文件中的所有文本，包括段落、文本框和艺术字，并去重
    :param docx_filename: docx文件路径
    :return: 提取的文本内容
    """
    text = []
    text_set = set()  # 用于去重的集合

    # 解压docx文件（其实是一个zip文件）
    with zipfile.ZipFile(docx_filename, 'r') as docx_zip:
        # 解析主文档内容
        with docx_zip.open('word/document.xml') as main_doc:
            tree = ET.parse(main_doc)
            root = tree.getroot()
            namespace = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}

            # 提取段落内容
            for paragraph in root.findall('.//w:p', namespace):
                # 将同一段落中的<w:t>文本拼接
                texts = [elem.text for elem in paragraph.findall('.//w:t', namespace) if elem.text]
                if texts:
                    paragraph_text = ''.join(texts)
                    if paragraph_text not in text_set:
                        text.append(paragraph_text)
                        text_set.add(paragraph_text)

        # 提取文本框内容（存储在word文件夹的不同部件中）
        for item in docx_zip.namelist():
            if 'word/drawings/' in item and item.endswith('.xml'):
                with docx_zip.open(item) as drawing_doc:
                    tree = ET.parse(drawing_doc)
                    root = tree.getroot()
                    for elem in root.findall('.//a:t', {'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'}):
                        if elem.text and elem.text not in text_set:
                            text.append(elem.text)
                            text_set.add(elem.text)

    return '\n'.join(text)  # 按行合并

def save_to_txt(content, txt_filename):
    """
    将内容保存到txt文件
    :param content: 要保存的文本内容
    :param txt_filename: 输出的txt文件路径
    """
    with open(txt_filename, 'w', encoding='utf-8') as file:
        file.write(content)
# 示例用法
txt_filename = 'output.txt'
content = extract_all_text_from_docx("中医诊断学.docx")

save_to_txt(content, txt_filename)


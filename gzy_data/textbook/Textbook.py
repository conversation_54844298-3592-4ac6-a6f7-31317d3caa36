import re
import pandas as pd


def process_txt_to_excel_with_subsections(txt_filename, excel_filename):
    # 打开txt文件并读取内容
    with open(txt_filename, 'r', encoding='utf-8') as file:
        content = file.readlines()

    # 用于保存数据的列表
    rows = []

    # 当前篇、章、节、内容标题、内容标题分点
    current_pian = ''
    current_chapter = ''
    current_section = ''
    current_title = ''
    current_subsection = ''

    # 用于存储内容
    current_content = []

    # 正则表达式匹配各级标题
    re_pian = re.compile(r"^\s*[上中下]篇\s*(.*?)(?<!\d)\s*$")  # 匹配“上篇”、“中篇”、“下篇”
    re_chapter = re.compile(r"^\s*第[一二三四五六七八九十]+章\s*(.*?)(?<!\d)\s*$")
    re_section = re.compile(r"^\s*第[一二三四五六七八九十]+节\s*(.*?)(?<!\d)\s*$")  # 匹配“第X节”
    re_subsection = re.compile(r"^\s*([一二三四五六七八九十]+、)\s*(.*?)$")  # 匹配“1、”
    # re_subsubsection = re.compile(r"^\s*(（[一二三四五六七八九十]+）)\s*(.*?)$")
    re_subsubsection = re.compile(r"^\s*(（[一二三四五六七八九十]+）|【.*】)\s*(.*?)$")  # 匹配“（1）”
    re_wrong = re.compile(r'^(【思考题】|复习思考题|【复习思考题】|附录|小结|思考题|主要参考书目).*$')  # 匹配“复习思考题”开头的行
    re_brak = re.compile(r'^(主要参考书目).*$')  # 匹配“复习思考题”开头的行
    re_page = re.compile(r".*\s{3}\d+$")

    # 标志变量，用来追踪是否有内容
    found_pian = False
    skip_content = False

    # 处理每一行
    for line in content:
        line = line.strip()  # 去除两端空白字符
        noBlank = line.replace(" ", "").replace("(", "（").replace(")", "）")  # 去除空格
        if not line:
            continue

        if re_page.match(line):
            continue

        if re_brak.match(noBlank):
            break

        # 如果遇到“无效内容”，则开始跳过
        if re_wrong.match(noBlank):
            skip_content = True
            continue  # 跳过该行内容

        # 一旦遇到下一个#标题，我们就停止跳过内容
        if re_pian.match(noBlank) or re_chapter.match(noBlank) or re_section.match(noBlank) or re_subsection.match(
                noBlank) or re_subsubsection.match(noBlank):
            skip_content = False

        if skip_content:
            continue  # 如果在跳过状态，直接跳过后续内容

        # 检测篇、章、节、内容标题、内容标题分点
        if re_pian.match(noBlank):
            # 保存之前的内容
            if current_content:
                rows.append([current_pian, current_chapter, current_section, current_title, current_subsection,
                             "\n".join(current_content)])
                current_content = []
            if not re_pian.match(noBlank).group(1):
                current_pian = re_pian.match(noBlank).group(0)
            else:
                current_pian = re_pian.match(noBlank).group(1)
            # 更新篇信息
            # current_pian = re_pian.match(noBlank).group(1)
            current_chapter = ''
            current_section = ''
            current_title = ''
            current_subsection = ''

            # 标记篇存在并继续处理
            found_pian = True

        elif re_chapter.match(noBlank):
            if current_content:
                rows.append([current_pian, current_chapter, current_section, current_title, current_subsection,
                             "\n".join(current_content)])
                current_content = []
            if not re_chapter.match(noBlank).group(1):
                current_chapter = re_chapter.match(noBlank).group(0)
            else:
                current_chapter = re_chapter.match(noBlank).group(1)
            current_section = ''
            current_title = ''
            current_subsection = ''
        elif re_section.match(noBlank):
            if current_content:
                rows.append([current_pian, current_chapter, current_section, current_title, current_subsection,
                             "\n".join(current_content)])
                current_content = []
            current_section = re_section.match(noBlank).group(1)
            current_title = ''
            current_subsection = ''
        elif re_subsection.match(noBlank):
            if current_content:
                rows.append([current_pian, current_chapter, current_section, current_title, current_subsection,
                             "\n".join(current_content)])
                current_content = []
            if not re_subsection.match(noBlank).group(2):
                current_title = re_subsection.match(noBlank).group(1)
            else:
                current_title = re_subsection.match(noBlank).group(2)
            current_subsection = ''
        elif re_subsubsection.match(noBlank):
            if current_content:
                rows.append([current_pian, current_chapter, current_section, current_title, current_subsection,
                             "\n".join(current_content)])
                current_content = []
            if not re_subsubsection.match(noBlank).group(2):
                current_subsection = re_subsubsection.match(noBlank).group(1)
            else:
                current_subsection = re_subsubsection.match(noBlank).group(2)
        else:
            # 否则是内容行
            current_content.append(line)

    # 将最后一段内容保存到列表中
    if current_content:
        rows.append([current_pian, current_chapter, current_section, current_title, current_subsection,
                     "\n".join(current_content)])

    # 如果篇存在并且没有被处理，处理篇与内容一起插入
    if found_pian and not current_chapter:
        rows.append([current_pian, current_chapter, current_section, current_title, "\n".join(current_content)])

    # 将数据转换为DataFrame
    df = pd.DataFrame(rows, columns=['篇', '章', '节', '内容标题', '内容标题分点', '内容'])

    # 保存为Excel文件
    df.to_excel(excel_filename, index=False)


def convert_punctuation(text):
    text = text.replace("（", "(").replace("）", ")")  # 中文括号转换为英文括号
    text = text.replace(",", "，")  # 中文逗号转换为英文逗号
    text = text.replace(" ", "")  # 去除空格
    return text


name = '针灸学.txt'
output_name = '教材-' + name.split('.')[0] + '.xlsx'
# 使用示例
process_txt_to_excel_with_subsections('fifth_txt/' + name, 'fifth_output/' + output_name)
print(name + "处理完成")

<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" type="image/png" href="/open-webui/static/favicon.png" />
		<link rel="icon" type="image/png" href="/open-webui/static/favicon-96x96.png" sizes="96x96" />
		<link rel="icon" type="image/svg+xml" href="/open-webui/static/favicon.svg" />
		<link rel="shortcut icon" href="/open-webui/static/favicon.ico" />
		<link rel="apple-touch-icon" sizes="180x180" href="/open-webui/static/apple-touch-icon.png" />
		<meta name="apple-mobile-web-app-title" content="Open WebUI" />

		<link rel="manifest" href="/open-webui/manifest.json" crossorigin="use-credentials" />
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1, maximum-scale=1, viewport-fit=cover"
		/>
		<meta name="theme-color" content="#171717" />
		<meta name="robots" content="noindex,nofollow" />
		<meta name="description" content="Open WebUI" />
		<link
			rel="search"
			type="application/opensearchdescription+xml"
			title="Open WebUI"
			href="/open-webui/opensearch.xml"
		/>
		<script src="/open-webui/static/loader.js" defer></script>

		<script>
			function resizeIframe(obj) {
				obj.style.height = obj.contentWindow.document.documentElement.scrollHeight + 'px';
			}
		</script>

		<script>
			// On page load or when changing themes, best to add inline in `head` to avoid FOUC
			(() => {
				const metaThemeColorTag = document.querySelector('meta[name="theme-color"]');
				const prefersDarkTheme = window.matchMedia('(prefers-color-scheme: dark)').matches;

				if (!localStorage?.theme) {
					localStorage.theme = 'system';
				}

				if (localStorage.theme === 'system') {
					document.documentElement.classList.add(prefersDarkTheme ? 'dark' : 'light');
					metaThemeColorTag.setAttribute('content', prefersDarkTheme ? '#171717' : '#ffffff');
				} else if (localStorage.theme === 'oled-dark') {
					document.documentElement.style.setProperty('--color-gray-800', '#101010');
					document.documentElement.style.setProperty('--color-gray-850', '#050505');
					document.documentElement.style.setProperty('--color-gray-900', '#000000');
					document.documentElement.style.setProperty('--color-gray-950', '#000000');
					document.documentElement.classList.add('dark');
					metaThemeColorTag.setAttribute('content', '#000000');
				} else if (localStorage.theme === 'light') {
					document.documentElement.classList.add('light');
					metaThemeColorTag.setAttribute('content', '#ffffff');
				} else if (localStorage.theme === 'her') {
					document.documentElement.classList.add('dark');
					document.documentElement.classList.add('her');
					metaThemeColorTag.setAttribute('content', '#983724');
				} else {
					document.documentElement.classList.add('dark');
					metaThemeColorTag.setAttribute('content', '#171717');
				}

				window.matchMedia('(prefers-color-scheme: dark)').addListener((e) => {
					if (localStorage.theme === 'system') {
						if (e.matches) {
							document.documentElement.classList.add('dark');
							document.documentElement.classList.remove('light');
							metaThemeColorTag.setAttribute('content', '#171717');
						} else {
							document.documentElement.classList.add('light');
							document.documentElement.classList.remove('dark');
							metaThemeColorTag.setAttribute('content', '#ffffff');
						}
					}
				});

				function setSplashImage() {
					const logo = document.getElementById('logo');
					const isDarkMode = document.documentElement.classList.contains('dark');

					if (isDarkMode) {
						const darkImage = new Image();
						darkImage.src = '/open-webui/static/splash-dark.png';

						darkImage.onload = () => {
							logo.src = '/open-webui/static/splash-dark.png';
							logo.style.filter = ''; // Ensure no inversion is applied if splash-dark.png exists
						};

						darkImage.onerror = () => {
							logo.style.filter = 'invert(1)'; // Invert image if splash-dark.png is missing
						};
					}
				}

				// Runs after classes are assigned
				window.onload = setSplashImage;
			})();
		</script>

		<title>Open WebUI</title>

		%sveltekit.head%
	</head>

	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>

		<div
			id="splash-screen"
			style="position: fixed; z-index: 100; top: 0; left: 0; width: 100%; height: 100%"
		>
			<style type="text/css" nonce="">
				html {
					overflow-y: scroll !important;
				}
			</style>

			<img
				id="logo"
				style="
					position: absolute;
					width: auto;
					height: 6rem;
					top: 44%;
					left: 50%;
					transform: translateX(-50%);
				"
				src="/open-webui/static/splash.png"
			/>

			<div
				style="
					position: absolute;
					top: 33%;
					left: 50%;

					width: 24rem;
					transform: translateX(-50%);

					display: flex;
					flex-direction: column;
					align-items: center;
				"
			>
				<img
					id="logo-her"
					style="width: auto; height: 13rem"
					src="/open-webui/static/splash.png"
					class="animate-pulse-fast"
				/>

				<div style="position: relative; width: 24rem; margin-top: 0.5rem">
					<div
						id="progress-background"
						style="
							position: absolute;
							width: 100%;
							height: 0.75rem;

							border-radius: 9999px;
							background-color: #fafafa9a;
						"
					></div>

					<div
						id="progress-bar"
						style="
							position: absolute;
							width: 0%;
							height: 0.75rem;
							border-radius: 9999px;
							background-color: #fff;
						"
						class="bg-white"
					></div>
				</div>
			</div>

			<!-- <span style="position: absolute; bottom: 32px; left: 50%; margin: -36px 0 0 -36px">
				Footer content
			</span> -->
		</div>
	</body>
</html>

<style type="text/css" nonce="">
	html {
		overflow-y: hidden !important;
	}

	#splash-screen {
		background: #fff;
	}

	html.dark #splash-screen {
		background: #000;
	}

	html.her #splash-screen {
		background: #983724;
	}

	#logo-her {
		display: none;
	}

	#progress-background {
		display: none;
	}

	#progress-bar {
		display: none;
	}

	html.her #logo {
		display: none;
	}

	html.her #logo-her {
		display: block;
		filter: invert(1);
	}

	html.her #progress-background {
		display: block;
	}

	html.her #progress-bar {
		display: block;
	}

	@media (max-width: 24rem) {
		html.her #progress-background {
			display: none;
		}

		html.her #progress-bar {
			display: none;
		}
	}

	@keyframes pulse {
		50% {
			opacity: 0.65;
		}
	}

	.animate-pulse-fast {
		animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}
</style>

{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' or '-1' untuk tiada tempoh luput.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(contoh `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(contoh `sh webui.sh --api`)", "(latest)": "(terkini)", "(Ollama)": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{user}}'s Chats": "Perbualan {{user}}", "{{webUIName}} Backend Required": "{{webUIName}} Backend diperlukan", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Model tugas digunakan semasa me<PERSON>n tugas seperti menjana tajuk untuk perbualan dan pertanyaan carian web.", "a user": "seorang pengguna", "About": "Mengenai", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "<PERSON><PERSON><PERSON><PERSON><PERSON> belum se<PERSON>ai", "Accurate information": "Informasi tepat", "Actions": "<PERSON><PERSON><PERSON>", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Pengguna Aktif", "Add": "Tambah", "Add a model ID": "", "Add a short description about what this model does": "Tambah penerangan ringkas tentang apa yang model ini boleh lakukan", "Add a tag": "Tambah tag", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "<PERSON><PERSON> a<PERSON>an k<PERSON>", "Add Files": "Tambah Fail", "Add Group": "", "Add Memory": "Tambah Memori", "Add Model": "Tambah Model", "Add Reaction": "", "Add Tag": "Tambah Tag", "Add Tags": "Tambah Tag", "Add text content": "", "Add User": "Tambah Pengguna", "Add User Group": "", "Adjusting these settings will apply changes universally to all users.": "Melaraskan tetapan ini akan menggunakan perubahan secara universal kepada semua pengguna.", "admin": "pentadbir", "Admin": "Pentadbir", "Admin Panel": "Panel Pentadbir", "Admin Settings": "Tetapan <PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Pentadbir mempunyai akses kepada semua alat pada setiap masa; pengguna memerlukan alat yang ditetapkan mengikut model da<PERSON> ruang kerja.", "Advanced Parameters": "Parameter Lanju<PERSON>", "Advanced Params": "Parameter Lanju<PERSON>", "All": "", "All Documents": "<PERSON><PERSON><PERSON>", "All models deleted successfully": "", "Allow Call": "", "Allow Chat Controls": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Benarkan Penghapusan Perbualan", "Allow Chat Edit": "", "Allow File Upload": "", "Allow Multiple Models in Chat": "", "Allow non-local voices": "Benark<PERSON> suara bukan tempatan ", "Allow Speech to Text": "", "Allow Temporary Chat": "", "Allow Text to Speech": "", "Allow User Location": "Benarkan <PERSON>", "Allow Voice Interruption in Call": "Benarkan gangguan suara dalam panggilan", "Allowed Endpoints": "", "Already have an account?": "Telah mempunyai akaun?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Amazing": "", "an assistant": "seorang pembantu", "Analyzed": "", "Analyzing...": "", "and": "dan", "and {{COUNT}} more": "", "and create a new shared link.": "dan cipta pautan kong<PERSON> baharu", "Android": "", "API Base URL": "URL Asas API", "API Key": "Kunci API", "API Key created.": "Kunci API dicipta", "API Key Endpoint Restrictions": "", "API keys": "Kekunci API", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "April", "Archive": "Arkib", "Archive All Chats": "Arkibkan Semu<PERSON>", "Archived Chats": "Perbualan yang di<PERSON>", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "<PERSON><PERSON><PERSON> anda pasti", "Arena Models": "", "Artifacts": "", "Ask": "", "Ask a question": "", "Assistant": "", "Attach file from knowledge": "", "Attention to detail": "<PERSON><PERSON><PERSON>", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "Audio", "August": "Ogos", "Auth": "", "Authenticate": "", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "Salin Response secara Automatik ke Papan Klip", "Auto-playback response": "Main semula respons secara automatik", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api Auth String", "AUTOMATIC1111 Base URL": "URL Asas AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "URL Asas AUTOMATIC1111 diperlukan.", "Available list": "", "Available Tools": "", "available!": "tersedia!", "Awful": "", "Azure AI Speech": "", "Azure Region": "", "Back": "Kembali", "Bad Response": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Banners": "<PERSON>and<PERSON>", "Base Model (From)": "<PERSON> <PERSON><PERSON> (<PERSON><PERSON>)", "Batch Size (num_batch)": "<PERSON><PERSON> (num_batch)", "before": "sebelum,", "Being lazy": "<PERSON><PERSON><PERSON>", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Bocha Search API Key": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Brave Search API Key": "Kunci API Carian Brave", "By {{name}}": "", "Bypass Embedding and Retrieval": "", "Calendar": "", "Call": "Hubung<PERSON>", "Call feature is not supported when using Web STT engine": "Ciri panggilan tidak disokong apabila menggunakan enjin Web STT", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON>", "Capabilities": "<PERSON><PERSON><PERSON><PERSON>", "Capture": "", "Certificate Path": "", "Change Password": "<PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "Perbualan", "Chat Background Image": "<PERSON><PERSON><PERSON>", "Chat Bubble UI": "<PERSON><PERSON><PERSON><PERSON>", "Chat Controls": "<PERSON><PERSON><PERSON>", "Chat direction": "<PERSON><PERSON>", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Perbualan", "Check Again": "<PERSON><PERSON><PERSON>", "Check for updates": "<PERSON><PERSON><PERSON> kema<PERSON> kini", "Checking for updates...": "<PERSON><PERSON> kini sedang disemak", "Choose a model before saving...": "<PERSON><PERSON>h model sebel<PERSON>", "Chunk Overlap": "Tindihan 'Çhunk'", "Chunk Size": "Saiz 'Chunk'", "Ciphers": "", "Citation": "<PERSON><PERSON><PERSON>", "Clear memory": "Kosongkan memori", "Clear Memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Klik disini untuk mendapatkan bantuan", "Click here to": "Klik disini untuk", "Click here to download user import template file.": "Klik disini untuk memuat turun fail templat import pengguna", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to see available models.": "", "Click here to select": "Klik disini untuk memilih", "Click here to select a csv file.": "Klik disini untuk memilih fail csv", "Click here to select a py file.": "<PERSON>lik disini untuk memilih fail py", "Click here to upload a workflow.json file.": "", "click here.": "klik disini.", "Click on the user role button to change a user's role.": "Klik pada butang peranan pengguna untuk menukar peranan pengguna", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Kebenaran untuk menulis di papan klip ditolak. <PERSON>la semak tetapan pelayan web anda untuk memberikan akses yang diperlukan", "Clone": "Klon", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "<PERSON><PERSON><PERSON>", "Code execution": "", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "<PERSON>d ber<PERSON>a diform<PERSON>", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "<PERSON><PERSON><PERSON><PERSON>", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "URL asas ComfyUI", "ComfyUI Base URL is required.": "URL asas ComfyUI diperlukan", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "<PERSON><PERSON>", "Completions": "", "Concurrent Requests": "<PERSON><PERSON><PERSON><PERSON>", "Configure": "", "Confirm": "<PERSON><PERSON><PERSON>", "Confirm Password": "<PERSON><PERSON><PERSON> kata laluan", "Confirm your action": "<PERSON><PERSON><PERSON> anda", "Confirm your new password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connections": "Sambungan", "Connections saved successfully": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Hubungi admin untuk akses WebUI", "Content": "Kandungan", "Content Extraction Engine": "", "Context Length": "Panjang Konteks", "Continue Response": "Teruskan Respons", "Continue with {{provider}}": "<PERSON><PERSON><PERSON> dengan {{provider}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "<PERSON><PERSON><PERSON>", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "Di<PERSON>in", "Copied shared chat URL to clipboard!": "Menyalin URL sembang kongsi ke papan klip", "Copied to clipboard": "", "Copy": "<PERSON><PERSON>", "Copy Formatted Text": "", "Copy last code block": "<PERSON>in Blok Kod Terakhir", "Copy last response": "<PERSON><PERSON> Re<PERSON>", "Copy Link": "<PERSON><PERSON>", "Copy to clipboard": "", "Copying to clipboard was successful!": "<PERSON><PERSON><PERSON> ke papan klip berjaya!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "", "Create a knowledge base": "", "Create a model": "Cipta model", "Create Account": "<PERSON><PERSON><PERSON>", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "<PERSON><PERSON><PERSON> kek<PERSON> baharu", "Create new secret key": "<PERSON><PERSON><PERSON> kek<PERSON>ci rahsia baharu", "Created at": "Dicipta di", "Created At": "<PERSON><PERSON><PERSON>", "Created by": "<PERSON><PERSON><PERSON> o<PERSON>h", "CSV Import": "Import CSV", "Ctrl+Enter to Send": "", "Current Model": "<PERSON> <PERSON><PERSON><PERSON>", "Current Password": "<PERSON><PERSON> la<PERSON>an se<PERSON>a", "Custom": "Tersuai", "Danger Zone": "", "Dark": "<PERSON><PERSON><PERSON>", "Database": "Pangkalan Data", "December": "Disember", "Default": "<PERSON><PERSON>", "Default (Open AI)": "", "Default (SentenceTransformers)": "<PERSON><PERSON> (SentenceTransformers)", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model’s built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "<PERSON> <PERSON>", "Default model updated": "Model la<PERSON> di<PERSON> kini", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Cadanga<PERSON>", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "<PERSON><PERSON><PERSON>", "Delete": "Padam", "Delete a model": "Padam Model", "Delete All Chats": "<PERSON><PERSON>", "Delete All Models": "", "Delete chat": "Padam perbualan", "Delete Chat": "Padam Perbualan", "Delete chat?": "Padam perbualan?", "Delete folder?": "", "Delete function?": "Padam fungsi?", "Delete Message": "", "Delete message?": "", "Delete prompt?": "Padam G<PERSON>?", "delete this link": "Padam pautan ini?", "Delete tool?": "Padam alat?", "Delete User": "Padam Pengguna", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} dipadam", "Deleted {{name}}": "{{name}} dipadam", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Penerangan", "Detect Artifacts Automatically": "", "Didn't fully follow instructions": "Tidak mengikut arahan se<PERSON>", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Connections settings updated": "", "Direct Tool Servers": "", "Disabled": "<PERSON><PERSON><PERSON><PERSON>", "Discover a function": "<PERSON><PERSON><PERSON> fungsi", "Discover a model": "Te<PERSON>i model", "Discover a prompt": "<PERSON><PERSON><PERSON> g<PERSON>", "Discover a tool": "<PERSON><PERSON><PERSON> alat", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON>, muat turun dan teroka fungsi tersuai", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON>, muat turun dan teroka gesaan tersuai", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON>, muat turun dan teroka alat tersuai", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON>, muat turun dan teroka model pratetap", "Dismissible": "Diketepikan", "Display": "", "Display Emoji in Call": "<PERSON><PERSON><PERSON>", "Display the username instead of You in the Chat": "<PERSON><PERSON>n nama pengguna dan buka<PERSON><PERSON> '<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "<PERSON>an pasang fungsi daripada sumber yang anda tidak percayai sepenuhnya.", "Do not install tools from sources you do not fully trust.": "<PERSON>an pasang alat daripada sumber yang anda tidak percaya sepenuhnya.", "Docling": "", "Docling Server URL required.": "", "Document": "Dokumen", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Dokumentasi", "Documents": "Dokumen", "does not make any external connections, and your data stays securely on your locally hosted server.": "tidak membuat sebarang sambungan luaran, dan data anda kekal selamat pada pelayan yang dihoskan ditempat anda", "Domain Filter List": "", "Don't have an account?": "Anda tidak mempunyai akaun?", "don't install random functions from sources you don't trust.": "jangan pasang mana-mana fungsi daripada sumber yang anda tidak percayai.", "don't install random tools from sources you don't trust.": "jangan pasang mana-mana alat daripada sumber yang anda tidak percayai.", "Don't like the style": "Tidak suka gaya ini", "Done": "Se<PERSON><PERSON>", "Download": "<PERSON><PERSON>", "Download as SVG": "", "Download canceled": "<PERSON><PERSON>", "Download Database": "Muat turun <PERSON>", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "Letakkan mana-mana fail di sini untuk ditambahkan pada perbualan", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "cth '30s','10m'. Unit masa yang sah ialah 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "Edit": "Edit", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "<PERSON>", "Edit User": "<PERSON>", "Edit User Group": "", "ElevenLabs": "ElevenLabs", "Email": "E-mel", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "Membenamkan Saiz Kelompok", "Embedding Model": "Model <PERSON><PERSON><PERSON>", "Embedding Model Engine": "Enjin Model <PERSON>", "Embedding model set to \"{{embedding_model}}\"": "Model Benamkan ditetapkan kepada \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "<PERSON><PERSON><PERSON>", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "<PERSON><PERSON><PERSON>", "Enabled": "<PERSON><PERSON><PERSON><PERSON>", "Enforce Temporary Chat": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "astikan fail CSV anda mengandungi 4 lajur dalam susunan ini: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON><PERSON> mesej {{role}} di sini", "Enter a detail about yourself for your LLMs to recall": "<PERSON><PERSON><PERSON><PERSON> butiran tentang diri anda untuk diingati oleh LLM anda", "Enter api auth string (e.g. username:password)": "<PERSON><PERSON><PERSON><PERSON> kekunci auth api ( cth nama pengguna:kata laluan )", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "Masukkan Kekunci API carian Brave", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "<PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON>'", "Enter Chunk Size": "<PERSON><PERSON><PERSON><PERSON> 'Chunk'", "Enter comma-seperated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter description": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter Github Raw URL": "Masukkan URL 'Github Raw'", "Enter Google PSE API Key": "Masukkan kunci API Google PSE", "Enter Google PSE Engine Id": "Masukkan Id Enjin Google PSE", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON><PERSON> (cth 512x512)", "Enter Jina API Key": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "<PERSON><PERSON><PERSON><PERSON> kod bahasa", "Enter Mistral API Key": "", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "Masukkan tag model (cth {{ modelTag }})", "Enter Mojeek Search API Key": "", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON><PERSON><PERSON> (cth 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Masukkan Skor", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Masukkan URL 'Searxng Query'", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "Masukkan Kunci API Serper", "Enter Serply API Key": "Masukkan Kunci API Serply", "Enter Serpstack API Key": "Masukkan Kunci API Serpstack", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "<PERSON><PERSON><PERSON><PERSON> urutan hentian", "Enter system prompt": "<PERSON><PERSON><PERSON><PERSON> gesaan sistem", "Enter system prompt here": "", "Enter Tavily API Key": "Masukkan Kunci API Tavily", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "Masukkan URL Pelayan Tika", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Masukkan 'Top K'", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Masukkan URL (cth http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Masukkan URL (cth http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "<PERSON><PERSON><PERSON><PERSON>l <PERSON>", "Enter Your Full Name": "<PERSON><PERSON><PERSON><PERSON>", "Enter your message": "<PERSON><PERSON><PERSON><PERSON> mesej anda", "Enter your name": "", "Enter your new password": "", "Enter Your Password": "<PERSON><PERSON><PERSON><PERSON>", "Enter Your Role": "<PERSON><PERSON><PERSON><PERSON>", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "<PERSON><PERSON>", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "<PERSON><PERSON><PERSON><PERSON>", "Explain": "", "Explain this section to me in more detail": "", "Explore the cosmos": "", "Export": "Eksport", "Export All Archived Chats": "", "Export All Chats (All Users)": "Eksport Semua Perbualan (Semua Pengguna)", "Export chat (.json)": "Eksport perbualan (.json)", "Export Chats": "Eksport Perbualan", "Export Config to JSON File": "", "Export Functions": "Eksport Fungsi", "Export Models": "Eksport Model", "Export Presets": "", "Export Prompts": "Eksport Gesaan", "Export to CSV": "", "Export Tools": "Eksport Alat", "External": "", "External Models": "<PERSON>", "Failed to add file.": "", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to create API Key.": "<PERSON><PERSON> mencipta kekunci API", "Failed to fetch models": "", "Failed to read clipboard contents": "Gagal membaca konten papan klip", "Failed to save connections": "", "Failed to save models configuration": "", "Failed to update settings": "<PERSON><PERSON> men<PERSON> tetapan", "Failed to upload file.": "", "Features": "", "Features Permissions": "", "February": "<PERSON><PERSON><PERSON>", "Feedback History": "", "Feedbacks": "", "Feel free to add specific details": "<PERSON>an ragu untuk menambah butiran khusus", "File": "Fail", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "<PERSON><PERSON>", "File not found.": "<PERSON>ail tidak dijumpai", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "Fail-Fail", "Filter is now globally disabled": "Tapisan kini dilumpuhkan secara global", "Filter is now globally enabled": "Tapisan kini diben<PERSON>an secara global", "Filters": "<PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Peniruan cap jari di<PERSON>, tidak dapat menggunakan nama pendek sebagai avatar. Lalai kepada imej profail asal", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "Strim 'chunks' respons luaran yang besar dengan lancar", "Focus chat input": "Fokus kepada input perbualan", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Followed instructions perfectly": "<PERSON><PERSON><PERSON><PERSON> arahan dengan sempurna", "Forge new paths": "", "Form": "<PERSON><PERSON>", "Format your variables using brackets like this:": "", "Forwards system user session credentials to authenticate": "", "Frequency Penalty": "<PERSON><PERSON><PERSON>", "Full Context Mode": "", "Function": "", "Function Calling": "", "Function created successfully": "<PERSON><PERSON><PERSON> berjaya dibuat", "Function deleted successfully": "<PERSON><PERSON><PERSON> ber<PERSON>a <PERSON>", "Function Description": "", "Function ID": "", "Function is now globally disabled": "Fungsi kini dilumpuhkan secara global", "Function is now globally enabled": "Fungsi kini dibenarkan secara global", "Function Name": "", "Function updated successfully": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "Functions": "<PERSON><PERSON><PERSON>", "Functions allow arbitrary code execution": "<PERSON><PERSON>i membe<PERSON>kan pelaks<PERSON>an kod sewenang-wenan<PERSON>a", "Functions allow arbitrary code execution.": "<PERSON><PERSON>i membe<PERSON>kan pelaksanaan kod sewenang-wenan<PERSON>.", "Functions imported successfully": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>a diimport", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "<PERSON><PERSON>", "Generate an image": "", "Generate Image": "<PERSON>", "Generate prompt pair": "", "Generating search query": "<PERSON>", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "Global", "Good Response": "Respons Baik", "Google Drive": "", "Google PSE API Key": "Kunci API Google PSE", "Google PSE Engine Id": "ID Enjin Google PSE", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "Haptic Feedback": "", "has no conversations.": "tidak mempunyai perbualan.", "Hello, {{name}}": "Hello, {{name}}", "Help": "Bantuan", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "<PERSON><PERSON><PERSON><PERSON>", "Hide Model": "", "Home": "", "Host": "", "How can I help you today?": "<PERSON><PERSON><PERSON> saya boleh membantu anda hari ini?", "How would you rate this response?": "", "Hybrid Search": "<PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Saya mengakui bahawa saya telah membaca dan saya memahami implikasi tindakan saya. Saya sedar tentang risiko yang berkaitan dengan melaksanakan kod sewenang-wenangnya dan saya telah mengesahkan kebolehpercayaan sumber tersebut.", "ID": "", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "", "Image Compression": "", "Image Generation": "", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON> (Percubaan)", "Image Generation Engine": "<PERSON><PERSON>", "Image Max Compression Size": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "<PERSON><PERSON><PERSON>", "Images": "<PERSON><PERSON><PERSON>", "Import Chats": "Import Perbualan", "Import Config from JSON File": "", "Import Functions": "Import <PERSON>", "Import Models": "Import Model", "Import Presets": "", "Import Prompts": "I<PERSON>rt G<PERSON>", "Import Tools": "Import Alat", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "<PERSON><PERSON><PERSON> bendera `-- api -auth` semasa <PERSON> stable-diffusion-webui ", "Include `--api` flag when running stable-diffusion-webui": "Ser<PERSON><PERSON> bendera `-- api ` se<PERSON>a <PERSON> stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Maklumat", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "<PERSON><PERSON><PERSON><PERSON>", "Install from Github URL": "Pa<PERSON> daripada URL Github", "Instant Auto-Send After Voice Transcription": "Hantar Secara Automatik Dengan Segera Selepas Transkripsi Suara", "Integration": "", "Interface": "<PERSON><PERSON><PERSON><PERSON>", "Invalid file format.": "", "Invalid JSON schema": "", "Invalid Tag": "Tag tidak sah", "is typing...": "", "January": "<PERSON><PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "sertai Discord kami untuk mendapatkan bantuan.", "JSON": "JSON", "JSON Preview": "Pratonton JSON", "July": "Jul<PERSON>", "June": "Jun", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "Tempoh Tamat JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep Alive": "Kekalkan Hidu<PERSON>", "Key": "", "Keyboard shortcuts": "Pintasan papan kekunci", "Knowledge": "<PERSON><PERSON><PERSON><PERSON>", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "", "Language": "Bahasa", "Language Locales": "", "Last Active": "<PERSON><PERSON><PERSON> aktif <PERSON> pada", "Last Modified": "<PERSON><PERSON><PERSON> terakhir pada", "Last reply": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Leave model field empty to use the default model.": "", "License": "", "Light": "<PERSON><PERSON>", "Listening...": "Mendengar...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "LLM boleh membuat kesilapan. Sahkan maklumat penting", "Loader": "", "Loading Kokoro.js...": "", "Local": "", "Local Models": "Model Tempatan", "Location access not allowed": "", "Logit Bias": "", "Lost": "", "LTR": "LTR", "Made by Open WebUI Community": "<PERSON><PERSON><PERSON> o<PERSON>h Komuniti OpenWebUI", "Make sure to enclose them with": "<PERSON>ikan untuk melampirkannya dengan", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "Urus", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Urus 'Pipelines'", "Manage Tool Servers": "", "March": "<PERSON>", "Max Tokens (num_predict)": "<PERSON><PERSON> ( num_predict )", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maksimum 3 model boleh dimuat turun serentak. Sila cuba sebentar lagi.", "May": "<PERSON>", "Memories accessible by LLMs will be shown here.": "<PERSON><PERSON>i yang boleh diakses oleh LLM akan ditunjukkan di sini.", "Memory": "<PERSON><PERSON><PERSON>", "Memory added successfully": "<PERSON><PERSON><PERSON> ber<PERSON>a di<PERSON>bah", "Memory cleared successfully": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "Memory deleted successfully": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "Memory updated successfully": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "<PERSON><PERSON><PERSON> yang anda hantar selepas membuat pautan anda tidak akan dikongsi. Pengguna dengan URL akan dapat melihat perbualan yang dikongsi.", "Min P": "P Minimum", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Model '{{ modelName }}' telah berjaya dimuat turun.", "Model '{{modelTag}}' is already in queue for downloading.": "Model '{{ modelTag }}' sudah dalam baris gilir untuk dimuat turun.", "Model {{modelId}} not found": "Model {{ modelId }} tidak dijumpai", "Model {{modelName}} is not vision capable": "Model {{ modelName }} tidak mempunyai keupayaan penglihatan", "Model {{name}} is now {{status}}": "Model {{name}} kini {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts image inputs": "", "Model created successfully!": "Model berjaya dibuat!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "<PERSON>uan sistem fail model di<PERSON><PERSON>. <PERSON><PERSON> model dip<PERSON><PERSON><PERSON> untuk kema<PERSON> kini, tidak boleh di<PERSON>.", "Model Filtering": "", "Model ID": "ID Model", "Model IDs": "", "Model Name": "", "Model not selected": "Model tidak dipilih", "Model Params": "Model Params", "Model Permissions": "", "Model updated successfully": "Model ber<PERSON><PERSON> di<PERSON><PERSON>i", "Modelfile Content": "Kandungan Modelfail", "Models": "Model", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "", "More": "<PERSON><PERSON>", "Name": "<PERSON><PERSON>", "Name your knowledge base": "", "Native": "", "New Chat": "Perbualan Baru", "New Folder": "", "New Password": "<PERSON><PERSON>", "new-channel": "", "No content found": "", "No content to speak": "Tiada kandungan untuk bercakap", "No distance available": "", "No feedbacks found": "", "No file selected": "Tiada fail dipilih", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No inference engine with management support found": "", "No knowledge found": "", "No memories to clear": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "Tiada keputusan dijumpai", "No search query generated": "<PERSON><PERSON><PERSON> per<PERSON><PERSON> car<PERSON> dijana", "No source available": "Tiada sumber tersedia", "No users were found.": "", "No valves to update": "Tiada 'valve' untuk dikemas kini", "None": "Tiada", "Not factually correct": "Tidak tepat secara fakta", "Not helpful": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Nota: <PERSON><PERSON> anda meneta<PERSON> skor minimum, carian hanya akan mengembalikan dokumen dengan skor lebih besar daripada atau sama dengan skor minimum.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Pemberitahuan", "November": "November", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "ID OAuth", "October": "Oktober", "Off": "<PERSON><PERSON>", "Okay, Let's Go!": "Baiklah, Jo<PERSON>!", "OLED Dark": "OLED Gelap", "Ollama": "Ollama", "Ollama API": "API Ollama", "Ollama API settings updated": "", "Ollama Version": "<PERSON><PERSON><PERSON>", "On": "<PERSON><PERSON><PERSON>", "OneDrive": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "<PERSON><PERSON> aksara alfanumerik dan sempang dibenarkan dalam rentetan arahan.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "<PERSON><PERSON>, didapati URL tidak sah. Sila semak semula dan cuba lagi.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "<PERSON><PERSON>, <PERSON><PERSON>kan kaedah yang tidak disokong (bahagian 'frontend' sahaja). Sila sediakan WebUI dari 'backend'.", "Open file": "", "Open in full screen": "", "Open new chat": "Buka perbualan baru", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) adalah lebih rendah daripada versi yang diperlukan iaitu (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Tetapan API OpenAI", "OpenAI API Key is required.": "Kekunci API OpenAI diperlukan", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "URL/Kekunci OpenAI diperlukan", "openapi.json Path": "", "or": "atau", "Organize your users": "", "Other": "Lain-lain", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "<PERSON><PERSON>", "Paste Large Text as File": "", "PDF document (.pdf)": "Dokumen PDF (.pdf)", "PDF Extract Images (OCR)": "Imej Ekstrak PDF (OCR)", "pending": "tertunda", "Permission denied when accessing media devices": "Tidak mendapat kebenaran apabila cuba mengakses peranti media", "Permission denied when accessing microphone": "Tidak mendapat kebenaran apabila cuba mengakses mikrofon", "Permission denied when accessing microphone: {{error}}": "Tidak mendapat kebenaran apabila cuba mengakses mikrofon: {{error}}", "Permissions": "", "Perplexity API Key": "", "Personalization": "<PERSON><PERSON><PERSON>", "Pin": "<PERSON>n", "Pinned": "Disemat", "Pioneer insights": "", "Pipeline deleted successfully": "'Pipeline' be<PERSON><PERSON>a dipadam", "Pipeline downloaded successfully": "'Pipeline' ber<PERSON>a dimuat turun", "Pipelines": "'Pipeline'", "Pipelines Not Detected": "'Pipeline' tidak di<PERSON>ui", "Pipelines Valves": "'Pipeline Valves'", "Plain text (.txt)": "<PERSON><PERSON> (.txt)", "Playground": "<PERSON><PERSON>", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "<PERSON>la semak dengan teliti amaran berikut:", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "", "Port": "", "Positive attitude": "Sikap positif", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Presence Penalty": "", "Previous 30 days": "30 hari sebelumnya", "Previous 7 days": "7 hari sebelumnya", "Private": "", "Profile Image": "<PERSON><PERSON><PERSON>", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Gesaan (cth <PERSON><PERSON><PERSON> saya fakta yang menyeronokkan tentang Kesultanan Melaka)", "Prompt Autocompletion": "", "Prompt Content": "Kandungan Gesaan", "Prompt created successfully": "", "Prompt suggestions": "Cadangan Gesaan", "Prompt updated successfully": "", "Prompts": "Gesaan", "Prompts Access": "", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "<PERSON><PERSON> \"{{ searchValue }}\" daripada Ollama.com", "Pull a model from Ollama.com": "Tarik model dari <PERSON>.com", "Query Generation Prompt": "", "RAG Template": "Templat RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read": "", "Read Aloud": "Baca dengan lantang", "Reasoning Effort": "", "Record voice": "<PERSON><PERSON><PERSON> suara", "Redirecting you to Open WebUI Community": "Me<PERSON><PERSON> anda ke Komuniti OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "<PERSON><PERSON><PERSON><PERSON> diri anda sebagai \"User\" (cth, \"Pengguna sedang belajar bahasa Sepanyol\")", "References from": "", "Refused when it shouldn't have": "Menolak dimana ia tidak sepatutnya", "Regenerate": "<PERSON> semula", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "<PERSON><PERSON>", "Relevance": "", "Relevance Threshold": "", "Remove": "Hapuskan", "Remove Model": "Hapuskan Model", "Rename": "<PERSON><PERSON><PERSON>", "Reorder Models": "", "Repeat Last N": "Ulang N Terakhir", "Repeat Penalty (Ollama)": "", "Reply in Thread": "", "Request Mode": "<PERSON><PERSON>", "Reranking Model": "Model 'Reranking'", "Reranking model disabled": "Model '<PERSON><PERSON><PERSON>' <PERSON><PERSON><PERSON><PERSON>", "Reranking model set to \"{{reranking_model}}\"": "Model 'Reranking' ditetapkan kepada \"{{reranking_model}}\"", "Reset": "Tetapkan Semula", "Reset All Models": "", "Reset Upload Directory": "Tetapkan Semula Direktori Muat Naik", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Pemberitahuan respons tidak boleh diaktifkan kerana kebenaran tapak web tidak diberi. Sila lawati tetapan pelayar web anda untuk memberikan akses yang dip<PERSON>an.", "Response splitting": "", "Result": "", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "<PERSON><PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Jalankan", "Running": "Sedang di<PERSON>", "Save": "Simpan", "Save & Create": "Simpan & Cipta", "Save & Update": "Simpan & Kemas <PERSON>", "Save As Copy": "", "Save Tag": "Simpan Tag", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Penyimpanan log perbualan terus ke storan pelayan web anda tidak lagi disokong. Sila luangkan sedikit masa untuk memuat turun dan memadam log perbualan anda dengan mengklik butang di bawah. <PERSON><PERSON> risau, anda boleh mengimport semula log perbualan anda dengan mudah melalui 'backend'", "Scroll to bottom when switching between branches": "<PERSON><PERSON><PERSON> ke bawah apabila bertukar antara cawangan", "Search": "<PERSON><PERSON>", "Search a model": "Cari Model", "Search Base": "", "Search Chats": "<PERSON><PERSON>", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "<PERSON><PERSON>", "Search Knowledge": "", "Search Models": "<PERSON>ian <PERSON>", "Search options": "", "Search Prompts": "<PERSON><PERSON>", "Search Result Count": "<PERSON><PERSON>", "Search the internet": "", "Search Tools": "Alat Carian", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "encari \"{{ searchQuery }}\"", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "URL Pertanyaan Searxng", "See readme.md for instructions": "<PERSON><PERSON> readme.md untuk arahan", "See what's new": "<PERSON>hat apa yang terbaru", "Seed": "<PERSON><PERSON>", "Select a base model": "<PERSON><PERSON><PERSON> model asas", "Select a engine": "<PERSON><PERSON><PERSON>", "Select a function": "<PERSON><PERSON><PERSON> fun<PERSON>i", "Select a group": "", "Select a model": "<PERSON><PERSON><PERSON> model", "Select a pipeline": "Pilih 'pipeline'", "Select a pipeline url": "Pilih url 'pipeline'", "Select a tool": "<PERSON><PERSON><PERSON> alat", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "", "Select Knowledge": "", "Select only one model to call": "<PERSON><PERSON><PERSON> hanya satu model untuk dipanggil", "Selected model(s) do not support image inputs": "Model dipilih tidak menyokong input imej", "Semantic distance to query": "", "Send": "Hantar", "Send a Message": "<PERSON><PERSON>", "Send message": "<PERSON><PERSON>", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "September", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Kunci API Serper", "Serply API Key": "Kunci API Serply", "Serpstack API Key": "Kunci API Serpstack", "Server connection verified": "Sambungan pelayan disahkan", "Set as default": "Tetapkan sebagai lalai", "Set CFG Scale": "", "Set Default Model": "Tetapkan <PERSON>ai", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Tetapkan model benamkan (cth {{model}})", "Set Image Size": "Tetapkan saiz imej", "Set reranking model (e.g. {{model}})": "Tetapkan model 'reranking' (cth {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "<PERSON><PERSON>", "Set Task Model": "Tetapkan Model Tugasan", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON><PERSON><PERSON>", "Set whisper model": "", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Tetapan", "Settings saved successfully!": "<PERSON><PERSON><PERSON> berjaya disimpan!", "Share": "Kong<PERSON>", "Share Chat": "Kongsi <PERSON>", "Share to Open WebUI Community": "Kongsi kepada Komuniti OpenWebUI", "Sharing Permissions": "", "Show": "Tunjukkan", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Show Model": "", "Show shortcuts": "<PERSON><PERSON><PERSON><PERSON><PERSON> pin<PERSON>an", "Show your support!": "<PERSON><PERSON><PERSON><PERSON>n sokongan anda!", "Showcased creativity": "eativiti yang dipamerkan", "Sign in": "<PERSON>ftar masuk", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "<PERSON><PERSON><PERSON> keluar", "Sign up": "<PERSON><PERSON><PERSON>", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "Sumber", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "<PERSON><PERSON> penge<PERSON>an pertuturan: {{error}}", "Speech-to-Text Engine": "<PERSON><PERSON>e-<PERSON>ks", "Stop": "", "Stop Sequence": "<PERSON><PERSON><PERSON>", "Stream Chat Response": "", "STT Model": "Model STT", "STT Settings": "Tetapan STT", "Subtitle (e.g. about the Roman Empire)": "<PERSON><PERSON> kata (cth tentang Kesultanan Melaka)", "Success": "<PERSON><PERSON><PERSON><PERSON>", "Successfully updated.": "<PERSON><PERSON><PERSON><PERSON>", "Suggested": "Cadangan", "Support": "Sokongan", "Support this plugin:": "Sokong plugin ini", "Sync directory": "", "System": "Sistem", "System Instructions": "", "System Prompt": "<PERSON><PERSON><PERSON>", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "<PERSON><PERSON><PERSON> untuk mengganggu", "Tasks": "", "Tavily API Key": "Kunci API Tavily", "Tavily Extract Depth": "", "Tell us more:": "<PERSON><PERSON><PERSON> kami lebih lan<PERSON>t", "Temperature": "<PERSON><PERSON>", "Template": "Templat", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech Engine": "<PERSON><PERSON>e-<PERSON><PERSON>", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "<PERSON><PERSON> kasih atas maklum balas anda!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Pembangun di sebalik 'plugin' ini adalah sukar<PERSON>wan yang bersemangat daripada komuniti. <PERSON><PERSON> anda mendapati 'plugin' ini membantu, sila pertimbangkan untuk menyumbang kepada pembangunannya.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Skor hendaklah berada diantara 0.0 (0%) dan 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "Theme": "<PERSON><PERSON>", "Thinking...": "Berfikir...", "This action cannot be undone. Do you wish to continue?": "Tindakan ini tidak boleh diubah semula kepada asal. <PERSON><PERSON><PERSON> anda ingin teruskan", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won’t appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Ini akan memastikan bahawa perbualan berharga anda disimpan dengan selamat ke pangkalan data 'backend' anda. <PERSON><PERSON> kasih!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "ni adalah ciri percu<PERSON>an, ia mungkin tidak berfungsi seperti yang diharapkan dan tertakluk kepada perubahan pada bila-bila masa.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "<PERSON>i akan memadam", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Thorough explanation": "<PERSON><PERSON><PERSON><PERSON>", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "URL Pelayan T<PERSON> dip<PERSON>lukan.", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Petua: <PERSON><PERSON> kini berbilang slot pembolehubah secara berturut-turut dengan menekan kekunci tab dalam input perbualan selepas setiap penggantian.", "Title": "Tajuk", "Title (e.g. Tell me a fun fact)": "Tajuk (cth Be<PERSON>hu saya fakta yang men<PERSON>kkan)", "Title Auto-Generation": "Penjanaan Auto Tajuk", "Title cannot be an empty string.": "Tajuk tidak boleh menjadi rentetan kosong", "Title Generation": "", "Title Generation Prompt": "<PERSON><PERSON><PERSON>", "TLS": "", "To access the available model names for downloading,": "<PERSON><PERSON>k mengakses nama model yang tersedia untuk dimuat turun,", "To access the GGUF models available for downloading,": "Untuk mengakses model GGUF yang tersedia untuk dimuat turun,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Untuk mengakses WebUI , sila hubungi pentadbir. Pentadbir boleh menguruskan status pengguna daripada Panel Pentadbiran", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "<PERSON>tuk memilih tindakan di sini, ta<PERSON><PERSON><PERSON><PERSON> pada ruang kerja \"Functions\" da<PERSON>u.", "To select filters here, add them to the \"Functions\" workspace first.": "Untuk memilih tapisan di sini, tamba<PERSON><PERSON><PERSON> pada ruang kerja \"Functions\" dahulu.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Untuk memilih kit alatan di sini, tambah<PERSON><PERSON> pada ruang kerja \"Tools\" dahulu.", "Toast notifications for new updates": "", "Today": "<PERSON>", "Toggle settings": "<PERSON><PERSON> ", "Toggle sidebar": "<PERSON><PERSON>", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "Token Untuk Teruskan Dalam Muat Semula Ko<PERSON>ks ( num_keep )", "Too verbose": "", "Tool created successfully": "Alat berjaya dibuat", "Tool deleted successfully": "<PERSON><PERSON> berjaya <PERSON>", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "Alat berjaya diimport", "Tool Name": "", "Tool Servers": "", "Tool updated successfully": "<PERSON><PERSON> berjaya dikemas kini", "Tools": "Alatan", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "Alatan ialah sistem panggilan fungsi dengan pelaksanaan kod sewenang-wenangnya", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution": "Alatan mempunyai sistem panggilan fungsi yang membolehkan pelaksanaan kod sewenang-wenangnya", "Tools have a function calling system that allows arbitrary code execution.": "Alatan mempunyai sistem panggilan fungsi yang membolehkan pelaksanaan kod sewenang-wenangnya.", "Tools Public Sharing": "", "Top K": "'Top K'", "Top K Reranker": "", "Top P": "'Top P'", "Transformers": "", "Trouble accessing Ollama?": "<PERSON><PERSON><PERSON> men<PERSON>?", "Trust Proxy Environment": "", "TTS Model": "Model TTS", "TTS Settings": "Tetapan TTS", "TTS Voice": "Suara TTS", "Type": "jenis", "Type Hugging Face Resolve (Download) URL": "Taip URL 'Hugging Face Resolve (Download)'", "Uh-oh! There was an issue with the response.": "", "UI": "UI", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "Nyahsemat<PERSON>", "Unravel secrets": "", "Untagged": "", "Update": "Kemaskini", "Update and Copy Link": "<PERSON><PERSON><PERSON> dan salin pautan", "Update for the latest features and improvements.": "", "Update password": "Kemas<PERSON> Kat<PERSON>", "Updated": "", "Updated at": "<PERSON>ke<PERSON><PERSON> pada", "Updated At": "", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "Muatnaik", "Upload a GGUF model": "Muatnaik model GGUF", "Upload directory": "", "Upload files": "", "Upload Files": "Muatnaik fail", "Upload Pipeline": "Muatnaik 'Pipeline'", "Upload Progress": "<PERSON><PERSON><PERSON><PERSON>", "URL": "", "URL Mode": "Mod URL", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "<PERSON><PERSON><PERSON>", "Use groups to group your users and assign permissions.": "", "Use Initials": "<PERSON><PERSON><PERSON> nama <PERSON>dek", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "se_mmap (Ollama)", "user": "pengguna", "User": "", "User location successfully retrieved.": "Lokasi pengguna berjaya diam<PERSON>.", "User Webhooks": "", "Username": "", "Users": "Pengguna", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "<PERSON><PERSON><PERSON>", "Valid time units:": "Unit masa yang sah:", "Valves": "'Valves'", "Valves updated": "'<PERSON><PERSON>' dikemaskini", "Valves updated successfully": "'<PERSON><PERSON>' be<PERSON><PERSON><PERSON>", "variable": "pem<PERSON><PERSON><PERSON><PERSON>", "variable to have them replaced with clipboard content.": "pembolehubah untuk ia digantikan dengan kandungan papan klip.", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "<PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "", "Voice": "<PERSON><PERSON>", "Voice Input": "", "Warning": "<PERSON><PERSON>", "Warning:": "<PERSON><PERSON>:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Amaran: <PERSON><PERSON> anda mengemas kini atau <PERSON>kar model benam anda, anda perlu mengimport semula semua dokumen.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "Web", "Web API": "API Web", "Web Loader Engine": "", "Web Search": "<PERSON>ian <PERSON>", "Web Search Engine": "Enjin Carian Web", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "URL 'Webhook'", "WebUI Settings": "Tetapan WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "What are you trying to achieve?": "", "What are you working on?": "", "What’s New in": "<PERSON><PERSON><PERSON><PERSON> yang terbaru dalam", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Local)", "Why?": "", "Widescreen Mode": "<PERSON><PERSON>", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "<PERSON><PERSON><PERSON>", "Workspace Permissions": "", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON> cadangan gesaan (cth Siapakah anda?)", "Write a summary in 50 words that summarizes [topic or keyword].": "<PERSON><PERSON> ring<PERSON>an dalam 50 patah perkataan yang meringkaskan [topik atau kata kunci].", "Write something...": "", "Write your model template content here": "", "Yesterday": "Semalam", "You": "<PERSON><PERSON>", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "<PERSON>a boleh memperibadikan interaksi anda dengan LLM dengan menambahkan memori melalui but<PERSON> '<PERSON><PERSON> di bawah, men<PERSON><PERSON><PERSON><PERSON> lebih membantu dan disesuaikan dengan anda.", "You cannot upload an empty file.": "", "You do not have permission to upload files": "", "You do not have permission to upload files.": "", "You have no archived conversations.": "Anda tidak mempunyai perbualan yang diark<PERSON>kan", "You have shared this chat": "Anda telah be<PERSON> perbualan ini", "You're a helpful assistant.": "<PERSON>a seorang pembantu yang bagus", "You're now logged in.": "Anda kini telah log masuk.", "Your account status is currently pending activation.": "Status akaun anda ialah sedang menunggu pengaktifan.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "<PERSON><PERSON><PERSON><PERSON> sumbangan anda akan dihantar terus kepada pembangun 'plugin'; Open WebUI tidak mengambil sebarang peratusan keuntungan daripadanya. <PERSON><PERSON><PERSON> bagaimanapun, platform pembiayaan yang dipilih mungkin mempunyai caj tersendiri.", "Youtube": "Youtube", "Youtube Language": "", "Youtube Proxy URL": ""}
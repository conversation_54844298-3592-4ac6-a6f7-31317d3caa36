{"-1 for no limit, or a positive integer for a specific limit": "-1 表示无限制，正整数表示具体限制", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' 或 '-1' 表示无过期时间。", "(e.g. `sh webui.sh --api --api-auth username_password`)": "（例如 `sh webui.sh --api --api-auth username_password`）", "(e.g. `sh webui.sh --api`)": "（例如 `sh webui.sh --api`）", "(latest)": "（最新版）", "(Ollama)": "(<PERSON><PERSON><PERSON>)", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "{{COUNT}} 个可用工具", "{{COUNT}} hidden lines": "{{COUNT}} 行被隐藏", "{{COUNT}} Replies": "{{COUNT}} 回复", "{{user}}'s Chats": "{{user}} 的对话记录", "{{webUIName}} Backend Required": "{{webUIName}} 需要后端服务", "*Prompt node ID(s) are required for image generation": "*图片生成需要 Prompt node ID", "A new version (v{{LATEST_VERSION}}) is now available.": "新版本（v{{LATEST_VERSION}}）现已发布。", "A task model is used when performing tasks such as generating titles for chats and web search queries": "任务模型用于执行生成对话标题和联网搜索查询等任务", "a user": "用户", "About": "关于", "Accept autocomplete generation / Jump to prompt variable": "接受自动补全生成 / 跳转到提示变量", "Access": "访问", "Access Control": "访问控制", "Accessible to all users": "对所有用户开放", "Account": "账号", "Account Activation Pending": "账号待激活", "Accurate information": "提供的信息很准确", "Actions": "自动化", "Activate": "激活", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "通过输入 \"/{{COMMAND}}\" 激活此命令", "Active Users": "当前在线用户", "Add": "添加", "Add a model ID": "添加一个模型ID", "Add a short description about what this model does": "添加有关该模型能力的简短描述", "Add a tag": "添加标签", "Add Arena Model": "添加竞技场模型", "Add Connection": "添加一个连接", "Add Content": "添加内容", "Add content here": "在此添加内容", "Add custom prompt": "添加自定义提示词", "Add Files": "添加文件", "Add Group": "添加权限组", "Add Memory": "添加记忆", "Add Model": "添加模型", "Add Reaction": "添加表情", "Add Tag": "添加标签", "Add Tags": "添加标签", "Add text content": "添加文本内容", "Add User": "添加用户", "Add User Group": "添加权限组", "Adjusting these settings will apply changes universally to all users.": "调整这些设置将会对所有用户应用更改。", "admin": "管理员", "Admin": "管理员联系方式", "Admin Panel": "管理员面板", "Admin Settings": "管理员设置", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "管理员拥有所有工具的访问权限；用户则需在工作空间中为每个模型单独分配工具。", "Advanced Parameters": "高级参数", "Advanced Params": "高级参数", "All": "全部", "All Documents": "所有文档", "All models deleted successfully": "所有模型删除成功", "Allow Call": "", "Allow Chat Controls": "允许对话高级设置", "Allow Chat Delete": "允许删除对话记录", "Allow Chat Deletion": "允许删除对话记录", "Allow Chat Edit": "允许编辑对话记录", "Allow File Upload": "允许上传文件", "Allow Multiple Models in Chat": "", "Allow non-local voices": "允许调用非本地音色", "Allow Speech to Text": "", "Allow Temporary Chat": "允许临时对话", "Allow Text to Speech": "", "Allow User Location": "允许获取您的位置", "Allow Voice Interruption in Call": "允许通话中的打断语音", "Allowed Endpoints": "允许的端点", "Already have an account?": "已经拥有账号了？", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "top_p 的替代方法，旨在确保质量和多样性之间的平衡。参数 p 表示相对于最可能令牌的概率，一个令牌被考虑的最小概率。例如，当 p=0.05 且最可能的令牌概率为 0.9 时，概率值小于 0.045 的词元将被过滤掉。", "Always": "保持", "Always Collapse Code Blocks": "始终折叠代码块", "Always Expand Details": "始终展开详细信息", "Amazing": "很棒", "an assistant": "一个助手", "Analyzed": "已分析", "Analyzing...": "正在分析...", "and": "和", "and {{COUNT}} more": "还有 {{COUNT}} 个", "and create a new shared link.": "并创建一个新的分享链接。", "Android": "", "API Base URL": "API 请求地址", "API Key": "API 密钥", "API Key created.": "API 密钥已创建。", "API Key Endpoint Restrictions": "API 密钥端点限制", "API keys": "API 密钥", "Application DN": "Application DN", "Application DN Password": "Application DN 密码", "applies to all users with the \"user\" role": "用于所有具有“用户”角色的用户", "April": "四月", "Archive": "归档", "Archive All Chats": "归档所有对话记录", "Archived Chats": "已归档对话", "archived-chat-export": "导出已归档对话", "Are you sure you want to clear all memories? This action cannot be undone.": "是否确认清除所有记忆？清除后无法还原", "Are you sure you want to delete this channel?": "是否确认删除此频道？", "Are you sure you want to delete this message?": "是否确认删除此消息？", "Are you sure you want to unarchive all archived chats?": "是否确认取消所有已归档的对话？", "Are you sure?": "是否确定？", "Arena Models": "启用竞技场匿名评价模型", "Artifacts": "Artifacts", "Ask": "提问", "Ask a question": "提问", "Assistant": "AI模型", "Attach file from knowledge": "从知识库附加文件", "Attention to detail": "注重细节", "Attribute for Mail": "邮箱属性", "Attribute for Username": "用户名属性", "Audio": "语音", "August": "八月", "Auth": "授权", "Authenticate": "认证", "Authentication": "身份验证", "Auto": "自动", "Auto-Copy Response to Clipboard": "自动复制回复到剪贴板", "Auto-playback response": "自动念出回复内容", "Autocomplete Generation": "输入框内容自动补全", "Autocomplete Generation Input Max Length": "输入框内容自动补全输入最大长度", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api 鉴权字符串", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 请求地址", "AUTOMATIC1111 Base URL is required.": "需要 AUTOMATIC1111 请求地址。", "Available list": "可用列表", "Available Tools": "可用工具", "available!": "版本可用！", "Awful": "糟糕", "Azure AI Speech": "Azure AI 语音", "Azure Region": "Azure 区域", "Back": "返回", "Bad Response": "点踩此回答", "Banners": "公告横幅", "Base Model (From)": "基础模型 (来自)", "Batch Size (num_batch)": "批大小 (num_batch)", "before": "对话", "Being lazy": "懒惰", "Beta": "Beta", "Bing Search V7 Endpoint": "Bing 搜索 V7 Endpoint", "Bing Search V7 Subscription Key": "Bing 搜索 V7 订阅密钥", "Bocha Search API Key": "Bocha Search API 密钥", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "为受限响应提升或惩罚特定标记。偏置值将被限制在 -100 到 100（包括两端）之间。（默认：无）", "Brave Search API Key": "Brave Search API 密钥", "By {{name}}": "由 {{name}} 提供", "Bypass Embedding and Retrieval": "绕过嵌入和检索", "Calendar": "日历", "Call": "呼叫", "Call feature is not supported when using Web STT engine": "使用 Web 语音转文字引擎时不支持呼叫功能。", "Camera": "摄像头", "Cancel": "取消", "Capabilities": "能力", "Capture": "截图", "Certificate Path": "证书路径", "Change Password": "更改密码", "Channel Name": "频道名称", "Channels": "频道", "Character": "字符", "Character limit for autocomplete generation input": "输入框内容自动补全输入的字符限制", "Chart new frontiers": "开拓新领域", "Chat": "对话", "Chat Background Image": "对话背景图片", "Chat Bubble UI": "气泡样式对话", "Chat Controls": "对话高级设置", "Chat direction": "对话样式方向", "Chat Overview": "对话概述", "Chat Permissions": "对话权限", "Chat Tags Auto-Generation": "自动生成对话标签", "Chats": "对话", "Check Again": "刷新重试", "Check for updates": "检查更新", "Checking for updates...": "正在检查更新...", "Choose a model before saving...": "保存前选择一个模型...", "Chunk Overlap": "块重叠 (<PERSON><PERSON>)", "Chunk Size": "块大小 (Chunk Size)", "Ciphers": "加密算法 (Ciphers)", "Citation": "引文", "Clear memory": "清除记忆", "Clear Memory": "清除记忆", "click here": "点击此处", "Click here for filter guides.": "点击此处查看 filter 指南。", "Click here for help.": "点击这里获取帮助。", "Click here to": "点击", "Click here to download user import template file.": "点击此处下载用户导入所需的模板文件。", "Click here to learn more about faster-whisper and see the available models.": "点击此处了解更多关于faster-whisper的信息，并查看可用的模型。", "Click here to see available models.": "单击此处查看可用模型。", "Click here to select": "点击这里选择", "Click here to select a csv file.": "点击此处选择 csv 文件。", "Click here to select a py file.": "点击此处选择 py 文件。", "Click here to upload a workflow.json file.": "点击此处上传 workflow.json 文件。", "click here.": "点击这里。", "Click on the user role button to change a user's role.": "点击角色前方的组别按钮以更改用户所属权限组。", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "写入剪贴板时被拒绝。请检查浏览器设置，授予必要权限。", "Clone": "复制", "Clone Chat": "克隆聊天", "Clone of {{TITLE}}": "{{TITLE}} 的副本", "Close": "关闭", "Code execution": "代码执行", "Code Execution": "代码执行", "Code Execution Engine": "代码执行引擎", "Code Execution Timeout": "代码执行超时时间", "Code formatted successfully": "代码格式化成功", "Code Interpreter": "代码解释器", "Code Interpreter Engine": "代码解释引擎", "Code Interpreter Prompt Template": "代码解释器提示词模板", "Collapse": "折叠", "Collection": "文件集", "Color": "颜色", "ComfyUI": "ComfyUI", "ComfyUI API Key": "ComfyUI API 密钥", "ComfyUI Base URL": "ComfyUI 请求地址", "ComfyUI Base URL is required.": "ComfyUI 请求地址为必需填写。", "ComfyUI Workflow": "ComfyUI 工作流", "ComfyUI Workflow Nodes": "ComfyUI 工作流节点", "Command": "命令", "Completions": "续写", "Concurrent Requests": "并发请求", "Configure": "配置", "Confirm": "确认", "Confirm Password": "确认密码", "Confirm your action": "确定吗？", "Confirm your new password": "确认新密码", "Connect to your own OpenAI compatible API endpoints.": "连接到你自己的与 OpenAI 兼容的 API 接口端点。", "Connect to your own OpenAPI compatible external tool servers.": "连接到您自己的兼容 OpenAPI 的外部工具服务器。", "Connection failed": "连接失败", "Connection successful": "连接成功", "Connections": "外部连接", "Connections saved successfully": "连接保存成功", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "约束推理模型的推理努力程度。仅适用于支持推理努力控制的特定提供商的推理模型。", "Contact Admin for WebUI Access": "请联系管理员以获取访问权限", "Content": "内容", "Content Extraction Engine": "内容提取引擎", "Context Length": "上下文长度", "Continue Response": "继续生成", "Continue with {{provider}}": "使用 {{provider}} 继续", "Continue with Email": "使用邮箱登录", "Continue with LDAP": "使用 LDAP 登录", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "控制消息文本如何拆分以用于 TTS 请求。“Punctuation”拆分为句子，“paragraphs”拆分为段落，“none”将消息保留为单个字符串。", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "控制生成文本中标记序列的重复度。较高的值（例如1.5）将更强烈地惩罚重复，而较低的值（例如1.1）则更为宽松。当值为1时，此功能将被禁用。", "Controls": "对话高级设置", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "控制输出文本中连贯性和多样性之间的平衡。较低的值将产生更加专注和连贯的文本。", "Copied": "已复制", "Copied shared chat URL to clipboard!": "已复制此对话分享链接至剪贴板！", "Copied to clipboard": "已复制到剪贴板", "Copy": "复制", "Copy Formatted Text": "", "Copy last code block": "复制最后一个代码块中的代码", "Copy last response": "复制最后一次回复内容", "Copy Link": "复制链接", "Copy to clipboard": "复制到剪贴板", "Copying to clipboard was successful!": "成功复制到剪贴板！", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "提供商必须正确配置 CORS 以允许来自 Open WebUI 的请求。", "Create": "创建", "Create a knowledge base": "创建知识库", "Create a model": "创建一个模型", "Create Account": "创建账号", "Create Admin Account": "创建管理员账号", "Create Channel": "创建频道", "Create Group": "创建权限组", "Create Knowledge": "创建知识", "Create new key": "创建新密钥", "Create new secret key": "创建新安全密钥", "Created at": "创建于", "Created At": "创建于", "Created by": "作者", "CSV Import": "通过 CSV 文件导入", "Ctrl+Enter to Send": "Ctrl+Enter 发送", "Current Model": "当前模型", "Current Password": "当前密码", "Custom": "自定义", "Danger Zone": "危险区域", "Dark": "暗色", "Database": "数据库", "December": "十二月", "Default": "默认", "Default (Open AI)": "默认 (OpenAI)", "Default (SentenceTransformers)": "默认（SentenceTransformers）", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model’s built-in tool-calling capabilities, but requires the model to inherently support this feature.": "默认模式通过在执行前调用一次工具，能够兼容更广泛的模型。原生模式利用模型内置的工具调用能力，但需要模型本身具备该功能的原生支持。", "Default Model": "默认模型", "Default model updated": "默认模型已更新", "Default Models": "默认模型", "Default permissions": "默认权限", "Default permissions updated successfully": "默认权限更新成功", "Default Prompt Suggestions": "默认提示词建议", "Default to 389 or 636 if TLS is enabled": "如果启用 TLS，则默认为 389 或 636", "Default to ALL": "默认为 ALL", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "默认进行分段检索以提取重点和相关内容 (推荐)", "Default User Role": "默认用户角色", "Delete": "删除", "Delete a model": "删除一个模型", "Delete All Chats": "删除所有对话记录", "Delete All Models": "删除所有模型", "Delete chat": "删除对话记录", "Delete Chat": "删除对话记录", "Delete chat?": "删除对话记录？", "Delete folder?": "删除分组？", "Delete function?": "删除函数？", "Delete Message": "删除消息", "Delete message?": "删除消息？", "Delete prompt?": "删除提示词？", "delete this link": "此处删除这个链接", "Delete tool?": "删除工具？", "Delete User": "删除用户", "Deleted {{deleteModelTag}}": "已删除 {{deleteModelTag}}", "Deleted {{name}}": "已删除 {{name}}", "Deleted User": "已删除用户", "Describe your knowledge base and objectives": "描述您的知识库和目标", "Description": "描述", "Detect Artifacts Automatically": "", "Didn't fully follow instructions": "没有完全遵照指示", "Direct": "直接", "Direct Connections": "直接连接", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "直接连接功能允许用户连接至其自有的、兼容 OpenAI 的 API 端点。", "Direct Connections settings updated": "直接连接设置已更新", "Direct Tool Servers": "直接连接工具服务器", "Disabled": "禁用", "Discover a function": "发现更多函数", "Discover a model": "发现更多模型", "Discover a prompt": "发现更多提示词", "Discover a tool": "发现更多工具", "Discover how to use Open WebUI and seek support from the community.": "了解如何使用 Open WebUI 并寻求社区支持。", "Discover wonders": "发现奇迹", "Discover, download, and explore custom functions": "发现、下载并探索更多函数", "Discover, download, and explore custom prompts": "发现、下载并探索更多自定义提示词", "Discover, download, and explore custom tools": "发现、下载并探索更多工具", "Discover, download, and explore model presets": "发现、下载并探索更多模型预设", "Dismissible": "是否可关闭", "Display": "显示", "Display Emoji in Call": "在通话中显示 Emoji 表情符号", "Display the username instead of You in the Chat": "在对话中显示用户名而不是“你”", "Displays citations in the response": "在回复中显示引用", "Dive into knowledge": "深入知识的海洋", "Do not install functions from sources you do not fully trust.": "切勿安装来源不完全可信的函数。", "Do not install tools from sources you do not fully trust.": "切勿安装来源不完全可信的工具。", "Docling": "<PERSON><PERSON>", "Docling Server URL required.": "需要提供 Docling 服务器 URL", "Document": "文档", "Document Intelligence": "Document Intelligence", "Document Intelligence endpoint and key required.": "需要 Document Intelligence 端点和密钥。", "Documentation": "帮助文档", "Documents": "文档", "does not make any external connections, and your data stays securely on your locally hosted server.": "不会与外部建立任何连接，您的数据会安全地存储在本地托管的服务器上。", "Domain Filter List": "域名过滤列表", "Don't have an account?": "没有账号？", "don't install random functions from sources you don't trust.": "切勿随意从不完全可信的来源安装函数。", "don't install random tools from sources you don't trust.": "切勿随意从不完全可信的来源安装工具。", "Don't like the style": "不喜欢这个文风", "Done": "完成", "Download": "下载", "Download as SVG": "下载为 SVG", "Download canceled": "下载已取消", "Download Database": "下载数据库", "Drag and drop a file to upload or select a file to view": "拖动文件上传或选择文件查看", "Draw": "平局", "Drop any files here to add to the conversation": "拖动文件到此处以添加到对话中", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "例如 '30s'，'10m'。有效的时间单位是秒：'s'，分：'m'，时：'h'。", "e.g. \"json\" or a JSON schema": "例如 \"json\" 或一个 JSON schema", "e.g. 60": "例如 '60'", "e.g. A filter to remove profanity from text": "例如：一个用于过滤文本中不当内容的过滤器", "e.g. My Filter": "例如：我的过滤器", "e.g. My Tools": "例如：我的工具", "e.g. my_filter": "例如：my_filter", "e.g. my_tools": "例如：my_tools", "e.g. Tools for performing various operations": "例如：用于执行各种操作的工具", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "Edit": "编辑", "Edit Arena Model": "编辑竞技场模型", "Edit Channel": "编辑频道", "Edit Connection": "编辑连接", "Edit Default Permissions": "编辑默认权限", "Edit Memory": "编辑记忆", "Edit User": "编辑用户", "Edit User Group": "编辑用户组", "ElevenLabs": "ElevenLabs", "Email": "电子邮箱", "Embark on adventures": "踏上冒险之旅", "Embedding": "嵌入", "Embedding Batch Size": "嵌入层批处理大小 (Embedding <PERSON><PERSON> Size)", "Embedding Model": "语义向量模型", "Embedding Model Engine": "语义向量模型引擎", "Embedding model set to \"{{embedding_model}}\"": "语义向量模型设置为 \"{{embedding_model}}\"", "Enable API Key": "启用 API 密钥", "Enable autocomplete generation for chat messages": "启用聊天消息的输入框内容自动补全", "Enable Code Execution": "启用代码执行", "Enable Code Interpreter": "启用代码解释器", "Enable Community Sharing": "启用分享至社区", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "启用内存锁定（mlock）以防止模型数据被交换出RAM。此选项将模型的工作集页面锁定在RAM中，确保它们不会被交换到磁盘。这可以通过避免页面错误和确保快速数据访问来帮助维持性能。", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "启用内存映射（mmap）以加载模型数据。此选项允许系统通过将磁盘文件视为在RAM中来使用磁盘存储作为RAM的扩展。这可以通过更快的数据访问来提高模型性能。然而，它可能无法在所有系统上正常工作，并且可能会消耗大量磁盘空间。", "Enable Message Rating": "启用回复评价", "Enable Mirostat sampling for controlling perplexity.": "启用 Mirostat 采样以控制困惑度", "Enable New Sign Ups": "允许新用户注册", "Enabled": "启用", "Enforce Temporary Chat": "强制临时聊天", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "确保您的 CSV 文件按以下顺序包含 4 列： 姓名、电子邮箱、密码、角色。", "Enter {{role}} message here": "在此处输入 {{role}} 的对话内容", "Enter a detail about yourself for your LLMs to recall": "输入一个关于你自己的详细信息，方便你的大语言模型记住这些内容", "Enter api auth string (e.g. username:password)": "输入 api 鉴权路径 (例如：用户名:密码)", "Enter Application DN": "输入 Application DN", "Enter Application DN Password": "输入 Application DN 密码", "Enter Bing Search V7 Endpoint": "输入 Bing Search V7 端点", "Enter Bing Search V7 Subscription Key": "输入 Bing Search V7 订阅密钥", "Enter Bocha Search API Key": "输入 Bocha Search API 密钥", "Enter Brave Search API Key": "输入 Brave Search API 密钥", "Enter certificate path": "输入证书路径", "Enter CFG Scale (e.g. 7.0)": "输入 CFG Scale (例如：7.0)", "Enter Chunk Overlap": "输入块重叠 (<PERSON><PERSON>lap)", "Enter Chunk Size": "输入块大小 (Chunk Size)", "Enter comma-seperated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "输入以逗号分隔的“token:bias_value”对（例如：5432:100, 413:-100）", "Enter description": "输入简介描述", "Enter Docling Server URL": "输入 Docling 服务器 URL", "Enter Document Intelligence Endpoint": "输入 Document Intelligence 端点", "Enter Document Intelligence Key": "输入 Document Intelligence 密钥", "Enter domains separated by commas (e.g., example.com,site.org)": "输入以逗号分隔的域名（例如：example.com,site.org）", "Enter Exa API Key": "输入 Exa API 密钥", "Enter Firecrawl API Base URL": "输入 Firecrawl API 请求地址", "Enter Firecrawl API Key": "输入 Firecrawl API 密钥", "Enter Github Raw URL": "输入 Github Raw 地址", "Enter Google PSE API Key": "输入 Google PSE API 密钥", "Enter Google PSE Engine Id": "输入 Google PSE 引擎 ID", "Enter Image Size (e.g. 512x512)": "输入图像分辨率 (例如：512x512)", "Enter Jina API Key": "输入 Jina API 密钥", "Enter Jupyter Password": "输入 Ju<PERSON>ter 密码", "Enter Jupyter Token": "输入 Ju<PERSON>ter 令牌", "Enter Jupyter URL": "输入 Jupyter URL", "Enter Kagi Search API Key": "输入 Kagi Search API 密钥", "Enter Key Behavior": "Enter 键行为", "Enter language codes": "输入语言代码", "Enter Mistral API Key": "输入 Mistral API 密钥", "Enter Model ID": "输入模型 ID", "Enter model tag (e.g. {{modelTag}})": "输入模型标签 (例如：{{modelTag}})", "Enter Mojeek Search API Key": "输入 Mojeek Search API 密钥", "Enter Number of Steps (e.g. 50)": "输入步骤数 (Steps) (例如：50)", "Enter Perplexity API Key": "输入 Perplexity API 密钥", "Enter Playwright Timeout": "输入 Playwright 超时时间", "Enter Playwright WebSocket URL": "输入 Playwright WebSocket URL", "Enter proxy URL (e.g. **************************:port)": "输入代理 URL (例如：https://用户名:密码@主机名:端口）", "Enter reasoning effort": "设置推理努力", "Enter Sampler (e.g. Euler a)": "输入 Sampler (例如：Euler a)", "Enter Scheduler (e.g. Karras)": "输入 Scheduler (例如：Karras)", "Enter Score": "输入评分", "Enter SearchApi API Key": "输入 SearchApi API 密钥", "Enter SearchApi Engine": "输入 SearchApi 引擎", "Enter Searxng Query URL": "输入 Searxng 查询地址", "Enter Seed": "输入 Seed", "Enter SerpApi API Key": "输入 SerpApi API 密钥", "Enter SerpApi Engine": "输入 SerpApi 引擎", "Enter Serper API Key": "输入 Serper API 密钥", "Enter Serply API Key": "输入 Serply API 密钥", "Enter Serpstack API Key": "输入 Serpstack API 密钥", "Enter server host": "输入服务器主机名 ", "Enter server label": "输入服务器标签", "Enter server port": "输入服务器端口", "Enter Sougou Search API sID": "输入搜狗搜索 API 的 Secret ID", "Enter Sougou Search API SK": "输入搜狗搜索 API 的 Secret Key", "Enter stop sequence": "输入停止序列 (Stop Sequence)", "Enter system prompt": "输入系统提示词 (Prompt)", "Enter system prompt here": "在这里输入系统提示词 (Prompt)", "Enter Tavily API Key": "输入 Tavily API 密钥", "Enter Tavily Extract Depth": "输入 Tavily 提取深度", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "输入 WebUI 的公共 URL。此 URL 将用于在通知中生成链接。", "Enter Tika Server URL": "输入 Tika 服务器地址", "Enter timeout in seconds": "输入以秒为单位的超时时间", "Enter to Send": "Enter 键发送", "Enter Top K": "输入 Top K", "Enter Top K Reranker": "输入 Top K Reranker", "Enter URL (e.g. http://127.0.0.1:7860/)": "输入地址 (例如：http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "输入地址 (例如：http://localhost:11434)", "Enter your current password": "输入当前密码", "Enter Your Email": "输入您的电子邮箱", "Enter Your Full Name": "输入您的名称", "Enter your message": "输入您的消息", "Enter your name": "输入您的名称", "Enter your new password": "输入新的密码", "Enter Your Password": "输入您的密码", "Enter Your Role": "输入您的权限组", "Enter Your Username": "输入您的用户名", "Enter your webhook URL": "输入您的 Webhook URL", "Error": "错误", "ERROR": "错误", "Error accessing Google Drive: {{error}}": "访问 Google 云端硬盘 出错： {{error}}", "Error uploading file: {{error}}": "上传文件时出错： {{error}}", "Evaluations": "竞技场评估", "Exa API Key": "Exa API 密钥", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "例如：(&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "例如：ALL", "Example: mail": "例如：mail", "Example: ou=users,dc=foo,dc=example": "例如：ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "例如：sAMAccountName 或 uid 或 userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "已达到最大授权人数，请联系支持人员提升授权人数。", "Exclude": "排除", "Execute code for analysis": "执行代码进行分析", "Executing **{{NAME}}**...": "正在执行 **{{NAME}}**...", "Expand": "展开", "Experimental": "实验性", "Explain": "解释", "Explain this section to me in more detail": "请更详细地向我解释这一部分", "Explore the cosmos": "探索宇宙", "Export": "导出", "Export All Archived Chats": "导出所有已存档对话", "Export All Chats (All Users)": "导出所有用户对话", "Export chat (.json)": "JSON 文件 (.json)", "Export Chats": "导出对话", "Export Config to JSON File": "导出配置信息至 JSON 文件中", "Export Functions": "导出函数", "Export Models": "导出模型", "Export Presets": "导出预设", "Export Prompts": "导出提示词", "Export to CSV": "导出到 CSV", "Export Tools": "导出工具", "External": "外部", "External Models": "外部模型", "Failed to add file.": "添加文件失败。", "Failed to connect to {{URL}} OpenAPI tool server": "无法连接到 {{URL}} OpenAPI 工具服务器", "Failed to create API Key.": "无法创建 API 密钥。", "Failed to fetch models": "无法获取模型", "Failed to read clipboard contents": "无法读取剪贴板内容", "Failed to save connections": "无法保存连接", "Failed to save models configuration": "无法保存模型配置", "Failed to update settings": "无法更新设置", "Failed to upload file.": "上传文件失败", "Features": "功能", "Features Permissions": "功能权限", "February": "二月", "Feedback History": "反馈历史", "Feedbacks": "反馈", "Feel free to add specific details": "欢迎补充具体细节", "File": "文件", "File added successfully.": "文件成功添加", "File content updated successfully.": "文件内容成功更新", "File Mode": "文件模式", "File not found.": "文件未找到。", "File removed successfully.": "文件成功删除", "File size should not exceed {{maxSize}} MB.": "文件大小不应超过 {{maxSize}} MB。", "File uploaded successfully": "文件上传成功。", "Files": "文件", "Filter is now globally disabled": "过滤器已全局禁用", "Filter is now globally enabled": "过滤器已全局启用", "Filters": "过滤器", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "检测到指纹伪造：无法使用姓名缩写作为头像。默认使用默认个人形象。", "Firecrawl API Base URL": "Firecrawl API 请求地址", "Firecrawl API Key": "Firecrawl API 密钥", "Fluidly stream large external response chunks": "流畅地传输外部大型响应块数据", "Focus chat input": "聚焦对话输入", "Folder deleted successfully": "分组删除成功", "Folder name cannot be empty": "分组名称不能为空", "Folder name cannot be empty.": "分组名称不能为空。", "Folder name updated successfully": "分组名称更新成功。", "Followed instructions perfectly": "完全按照指示执行", "Forge new paths": "开拓新道路", "Form": "手动创建", "Format your variables using brackets like this:": "使用括号格式化你的变量，如下所示：", "Forwards system user session credentials to authenticate": "转发系统用户 session 凭证以进行身份​​验证", "Frequency Penalty": "频率惩罚", "Full Context Mode": "完整上下文模式", "Function": "函数", "Function Calling": "函数调用 (Function Calling)", "Function created successfully": "函数创建成功", "Function deleted successfully": "函数删除成功", "Function Description": "函数描述", "Function ID": "函数ID", "Function is now globally disabled": "函数全局已禁用", "Function is now globally enabled": "函数全局已启用", "Function Name": "函数名称", "Function updated successfully": "函数更新成功", "Functions": "函数", "Functions allow arbitrary code execution": "注意：函数有权执行任意代码", "Functions allow arbitrary code execution.": "注意：函数有权执行任意代码。", "Functions imported successfully": "函数导入成功", "Gemini": "Gemini", "Gemini API Config": "Gemini API 配置", "Gemini API Key is required.": "需要 Gemini API 密钥。", "General": "通用", "Generate an image": "生成图像", "Generate Image": "生成图像", "Generate prompt pair": "生成提示对", "Generating search query": "生成搜索查询", "Get started": "开始使用", "Get started with {{WEBUI_NAME}}": "开始使用 {{WEBUI_NAME}}", "Global": "全局", "Good Response": "点赞此回答", "Google Drive": "Google 云端硬盘", "Google PSE API Key": "Google PSE API 密钥", "Google PSE Engine Id": "Google PSE 引擎 ID", "Group created successfully": "权限组创建成功", "Group deleted successfully": "权限组删除成功", "Group Description": "权限组描述", "Group Name": "权限组名称", "Group updated successfully": "权限组更新成功", "Groups": "权限组", "Haptic Feedback": "震动反馈", "has no conversations.": "没有对话。", "Hello, {{name}}": "您好，{{name}}", "Help": "帮助", "Help us create the best community leaderboard by sharing your feedback history!": "分享您的反馈历史记录，共建最佳模型社区排行榜！", "Hex Color": "十六进制颜色代码", "Hex Color - Leave empty for default color": "十六进制颜色代码 - 留空使用默认颜色", "Hide": "隐藏", "Hide Model": "隐藏模型", "Home": "主页", "Host": "主机", "How can I help you today?": "有什么我能帮您的吗？", "How would you rate this response?": "您如何评价这个回应？", "Hybrid Search": "混合搜索", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "我已阅读并理解我的行为所带来的影响，明白执行任意代码所涉及的风险。且我已验证代码来源可信度。", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "点燃好奇心", "Image": "图像生成", "Image Compression": "图像压缩", "Image Generation": "图像生成", "Image Generation (Experimental)": "图像生成（实验性）", "Image Generation Engine": "图像生成引擎", "Image Max Compression Size": "图像压缩后最大分辨率", "Image Prompt Generation": "图像提示词生成", "Image Prompt Generation Prompt": "用于生成图像提示词的提示词", "Image Settings": "图像设置", "Images": "图像", "Import Chats": "导入对话记录", "Import Config from JSON File": "导入 JSON 文件中的配置信息", "Import Functions": "导入函数", "Import Models": "导入模型", "Import Presets": "导入预设", "Import Prompts": "导入提示词", "Import Tools": "导入工具", "Include": "包括", "Include `--api-auth` flag when running stable-diffusion-webui": "运行 stable-diffusion-webui 时包含 `--api-auth` 参数", "Include `--api` flag when running stable-diffusion-webui": "运行 stable-diffusion-webui 时包含 `--api` 参数", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "影响算法对生成文本反馈的响应速度。较低的学习率将导致调整更慢，而较高的学习率将使算法反应更灵敏。", "Info": "信息", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "注入整个内容作为上下文进行综合处理，适用于复杂查询", "Input commands": "输入命令", "Install from Github URL": "从 Github URL 安装", "Instant Auto-Send After Voice Transcription": "语音转录文字后即时自动发送", "Integration": "集成", "Interface": "界面", "Invalid file format.": "无效文件格式。", "Invalid JSON schema": "无效的 JSON schema", "Invalid Tag": "无效标签", "is typing...": "输入中...", "January": "一月", "Jina API Key": "Jina API 密钥", "join our Discord for help.": "加入我们的 Discord 寻求帮助。", "JSON": "JSON", "JSON Preview": "JSON 预览", "July": "七月", "June": "六月", "Jupyter Auth": "<PERSON><PERSON><PERSON>", "Jupyter URL": "Jupyter URL", "JWT Expiration": "JWT 过期", "JWT Token": "JWT 令牌", "Kagi Search API Key": "Kagi 搜索 API 密钥", "Keep Alive": "保持活动", "Key": "密匙", "Keyboard shortcuts": "键盘快捷键", "Knowledge": "知识库", "Knowledge Access": "访问知识库", "Knowledge created successfully.": "知识成功创建", "Knowledge deleted successfully.": "知识成功删除", "Knowledge Public Sharing": "知识公开共享", "Knowledge reset successfully.": "知识成功重置", "Knowledge updated successfully": "知识成功更新", "Kokoro.js (Browser)": "Kokoro.js (Browser)", "Kokoro.js Dtype": "Kokoro.js Dtype", "Label": "标签", "Landing Page Mode": "默认主页样式", "Language": "语言", "Language Locales": "", "Last Active": "最后在线时间", "Last Modified": "最后修改时间", "Last reply": "最后回复", "LDAP": "LDAP", "LDAP server updated": "LDAP 服务器已更新", "Leaderboard": "排行榜", "Learn more about OpenAPI tool servers.": "进一步了解 OpenAPI 工具服务器。", "Leave empty for unlimited": "留空表示无限制", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "留空以包含来自 \"{{url}}/api/tags\" 端点的所有模型", "Leave empty to include all models from \"{{url}}/models\" endpoint": "留空以包含来自 \"{{url}}/models\" 端点的所有模型", "Leave empty to include all models or select specific models": "留空表示包含所有模型或请选择模型", "Leave empty to use the default prompt, or enter a custom prompt": "留空以使用默认提示词，或输入自定义提示词。", "Leave model field empty to use the default model.": "将模型字段留空以使用默认模型。", "License": "授权", "Light": "浅色", "Listening...": "正在倾听...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "大语言模型可能会生成误导性错误信息，请对关键信息加以验证。", "Loader": "加载器", "Loading Kokoro.js...": "载入 Kokoro.js...", "Local": "本地", "Local Models": "本地模型", "Location access not allowed": "不允许访问位置信息", "Logit Bias": "Logit 偏置", "Lost": "落败", "LTR": "从左至右", "Made by Open WebUI Community": "由 OpenWebUI 社区制作", "Make sure to enclose them with": "确保将它们包含在内", "Make sure to export a workflow.json file as API format from ComfyUI.": "确保从 ComfyUI 导出 API 格式的 workflow.json 文件。", "Manage": "管理", "Manage Direct Connections": "管理直接连接", "Manage Models": "管理模型", "Manage Ollama": "管理 <PERSON><PERSON>ma", "Manage Ollama API Connections": "管理Ollama API连接", "Manage OpenAI API Connections": "管理OpenAI API连接", "Manage Pipelines": "管理 Pipeline", "Manage Tool Servers": "管理工具服务器", "March": "三月", "Max Tokens (num_predict)": "最大 Token 数量 (num_predict)", "Max Upload Count": "最大上传数量", "Max Upload Size": "最大上传大小", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "最多可以同时下载 3 个模型，请稍后重试。", "May": "五月", "Memories accessible by LLMs will be shown here.": "大语言模型可访问的记忆将在此显示。", "Memory": "记忆", "Memory added successfully": "记忆添加成功", "Memory cleared successfully": "记忆清除成功", "Memory deleted successfully": "记忆删除成功", "Memory updated successfully": "记忆更新成功", "Merge Responses": "合并回复", "Message rating should be enabled to use this feature": "要使用此功能，应先启用回复评价功能", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "创建链接后发送的消息不会被共享。具有 URL 的用户将能够查看共享对话。", "Min P": "<PERSON>", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "Mistral OCR": "Mistral OCR", "Mistral OCR API Key required.": "需要 Mistral OCR API 密钥。", "Model": "模型", "Model '{{modelName}}' has been successfully downloaded.": "模型'{{modelName}}'已成功下载。", "Model '{{modelTag}}' is already in queue for downloading.": "模型'{{modelTag}}'已在下载队列中。", "Model {{modelId}} not found": "未找到模型 {{modelId}}", "Model {{modelName}} is not vision capable": "模型 {{modelName}} 不支持视觉能力", "Model {{name}} is now {{status}}": "模型 {{name}} 现在是 {{status}}", "Model {{name}} is now hidden": "模型 {{name}} 现已隐藏", "Model {{name}} is now visible": "模型 {{name}} 现已可见", "Model accepts image inputs": "模型接受图像输入", "Model created successfully!": "模型创建成功！", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "检测到模型文件系统路径，无法继续进行。更新操作需要提供模型简称。", "Model Filtering": "模型白名单", "Model ID": "模型 ID", "Model IDs": "模型 ID", "Model Name": "模型名称", "Model not selected": "未选择模型", "Model Params": "模型参数", "Model Permissions": "模型权限", "Model updated successfully": "模型更新成功", "Modelfile Content": "模型文件内容", "Models": "模型", "Models Access": "访问模型列表", "Models configuration saved successfully": "模型配置保存成功", "Models Public Sharing": "模型公开分享", "Mojeek Search API Key": "Mojeek Search API 密钥", "more": "更多", "More": "更多", "Name": "名称", "Name your knowledge base": "为您的知识库命名", "Native": "原生", "New Chat": "新对话", "New Folder": "新文件夹", "New Password": "新密码", "new-channel": "新频道", "No content found": "未发现内容", "No content to speak": "没有内容可朗读", "No distance available": "没有可用距离", "No feedbacks found": "暂无任何反馈", "No file selected": "未选中文件", "No files found.": "未找到文件。", "No groups with access, add a group to grant access": "没有权限组，请添加一个权限组以授予访问权限", "No HTML, CSS, or JavaScript content found.": "未找到 HTML、CSS 或 JavaScript 内容。", "No inference engine with management support found": "未找到支持管理的推理引擎", "No knowledge found": "未找到知识", "No memories to clear": "记忆为空，无须清理", "No model IDs": "没有模型 ID", "No models found": "未找到任何模型", "No models selected": "未选择任何模型", "No results found": "未找到结果", "No search query generated": "未生成搜索查询", "No source available": "没有可用来源", "No users were found.": "未找到用户", "No valves to update": "没有需要更新的值", "None": "无", "Not factually correct": "事实并非如此", "Not helpful": "无帮助", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "注意：如果设置了最低分数，搜索只会返回分数大于或等于最低分数的文档。", "Notes": "笔记", "Notification Sound": "通知提示音", "Notification Webhook": "通知 Webhook", "Notifications": "桌面通知", "November": "十一月", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth ID", "October": "十月", "Off": "关闭", "Okay, Let's Go!": "确认，开始使用！", "OLED Dark": "黑色", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "Ollama API设置已更新", "Ollama Version": "Ollama 版本", "On": "开启", "OneDrive": "OneDrive", "Only alphanumeric characters and hyphens are allowed": "只允许使用英文字母，数字 (0-9) 以及连字符 (-)", "Only alphanumeric characters and hyphens are allowed in the command string.": "命令字符串中只允许使用英文字母，数字 (0-9) 以及连字符 (-)。", "Only collections can be edited, create a new knowledge base to edit/add documents.": "只能编辑文件集，创建一个新的知识库来编辑/添加文件。", "Only select users and groups with permission can access": "只有具有权限的用户和组才能访问", "Oops! Looks like the URL is invalid. Please double-check and try again.": "此链接似乎为无效链接。请检查后重试。", "Oops! There are files still uploading. Please wait for the upload to complete.": "稍等！还有文件正在上传。请等待上传完成。", "Oops! There was an error in the previous response.": "糟糕！有一个错误出现在了之前的回复中。", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "你正在使用不被支持的方法（仅运行前端服务）。需要后端提供 WebUI 服务。", "Open file": "打开文件", "Open in full screen": "全屏打开", "Open new chat": "打开新对话", "Open WebUI can use tools provided by any OpenAPI server.": "Open WebUI 可使用任何 OpenAPI 服务器提供的工具。", "Open WebUI uses faster-whisper internally.": "Open WebUI 使用内置 faster-whisper。", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI 使用 SpeechT5 和 CMU Arctic speaker embedding。", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "当前 Open WebUI 版本 (v{{OPEN_WEBUI_VERSION}}) 低于所需的版本 (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API 配置", "OpenAI API Key is required.": "需要 OpenAI API 密钥。", "OpenAI API settings updated": "OpenAI API 设置已更新", "OpenAI URL/Key required.": "需要 OpenAI URL/Key", "openapi.json Path": "openapi.json 路径", "or": "或", "Organize your users": "组织用户", "Other": "其他", "OUTPUT": "输出", "Output format": "输出格式", "Overview": "概述", "page": "页", "Password": "密码", "Paste Large Text as File": "粘贴大文本为文件", "PDF document (.pdf)": "PDF 文档 (.pdf)", "PDF Extract Images (OCR)": "PDF 图像处理 (使用 OCR)", "pending": "待激活", "Permission denied when accessing media devices": "申请媒体设备权限被拒绝", "Permission denied when accessing microphone": "申请麦克风权限被拒绝", "Permission denied when accessing microphone: {{error}}": "申请麦克风权限被拒绝：{{error}}", "Permissions": "权限", "Perplexity API Key": "Perplexity API 密钥", "Personalization": "个性化", "Pin": "置顶", "Pinned": "已置顶", "Pioneer insights": "先锋的见解", "Pipeline deleted successfully": "Pipeline 删除成功", "Pipeline downloaded successfully": "Pipeline 下载成功", "Pipelines": "Pipeline", "Pipelines Not Detected": "未检测到 Pipeline", "Pipelines Valves": "Pipeline 值", "Plain text (.txt)": "TXT 文档 (.txt)", "Playground": "AI 对话游乐场", "Playwright Timeout (ms)": "Playwright 超时时间 (ms)", "Playwright WebSocket URL": "Playwright WebSocket URL", "Please carefully review the following warnings:": "请仔细阅读以下警告信息：", "Please do not close the settings page while loading the model.": "加载模型时请不要关闭设置页面。", "Please enter a prompt": "请输入一个 Prompt", "Please enter a valid path": "请输入有效路径", "Please enter a valid URL": "请输入有效 URL", "Please fill in all fields.": "请填写所有字段。", "Please select a model first.": "请先选择一个模型。", "Please select a model.": "请选择一个模型。", "Please select a reason": "请选择原因", "Port": "端口", "Positive attitude": "积极的态度", "Prefix ID": "Prefix ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefix ID 用于通过为模型 ID 添加前缀来避免与其他连接发生冲突 - 留空则禁用此功能", "Presence Penalty": "重复惩罚 (Presence Penalty)", "Previous 30 days": "过去 30 天", "Previous 7 days": "过去 7 天", "Private": "私有", "Profile Image": "用户头像", "Prompt": "提示词 (Prompt)", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "提示词（例如：给我讲一个关于罗马帝国的趣事。）", "Prompt Autocompletion": "提示词自动补全", "Prompt Content": "提示词内容", "Prompt created successfully": "提示词创建成功", "Prompt suggestions": "提示词建议", "Prompt updated successfully": "提示词更新成功", "Prompts": "提示词", "Prompts Access": "访问提示词", "Prompts Public Sharing": "提示词公开分享", "Public": "公共", "Pull \"{{searchValue}}\" from Ollama.com": "从 Ollama.com 拉取 \"{{searchValue}}\"", "Pull a model from Ollama.com": "从 Ollama.com 拉取一个模型", "Query Generation Prompt": "查询生成提示词", "RAG Template": "RAG 提示词模板", "Rating": "评价", "Re-rank models by topic similarity": "根据主题相似性对模型重新排序", "Read": "只读", "Read Aloud": "朗读", "Reasoning Effort": "推理努力", "Record voice": "录音", "Redirecting you to Open WebUI Community": "正在将您重定向到 OpenWebUI 社区", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "降低生成无意义内容的概率。较高的值（如100）将产生更多样化的回答，而较低的值（如10）则更加保守。", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "使用\"User\" (用户) 来指代自己（例如：“User 正在学习西班牙语”）", "References from": "来自", "Refused when it shouldn't have": "无理拒绝", "Regenerate": "重新生成", "Reindex": "重建索引", "Reindex Knowledge Base Vectors": "重建知识库向量", "Release Notes": "更新日志", "Relevance": "相关性", "Relevance Threshold": "", "Remove": "移除", "Remove Model": "移除模型", "Rename": "重命名", "Reorder Models": "重新排序模型", "Repeat Last N": "重复最后 N 次", "Repeat Penalty (Ollama)": "重复惩罚 (<PERSON><PERSON><PERSON>)", "Reply in Thread": "在主题中回复", "Request Mode": "请求模式", "Reranking Model": "重排模型", "Reranking model disabled": "重排模型已禁用", "Reranking model set to \"{{reranking_model}}\"": "重排模型设置为 \"{{reranking_model}}\"", "Reset": "重置", "Reset All Models": "重置所有模型", "Reset Upload Directory": "重置上传目录", "Reset Vector Storage/Knowledge": "重置向量存储/知识", "Reset view": "重置视图", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "无法激活回复时发送通知。请检查浏览器设置，并授予必要的访问权限。", "Response splitting": "拆分回复", "Result": "结果", "Retrieval": "检索", "Retrieval Query Generation": "检索查询生成", "Rich Text Input for Chat": "对话富文本输入", "RK": "排名", "Role": "权限组", "Rosé Pine": "玫瑰松木", "Rosé Pine Dawn": "玫瑰松木·晨曦", "RTL": "从右至左", "Run": "运行", "Running": "运行中", "Save": "保存", "Save & Create": "保存并创建", "Save & Update": "保存并更新", "Save As Copy": "另存为副本", "Save Tag": "保存标签", "Saved": "已保存", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "我们不再支持将聊天记录直接保存到浏览器的存储空间。请点击下面的按钮下载并删除您的聊天记录。别担心，您可以轻松地将聊天记录重新导入到后台。", "Scroll to bottom when switching between branches": "在分支间切换时滚动到底部", "Search": "搜索", "Search a model": "搜索模型", "Search Base": "搜索库", "Search Chats": "搜索对话", "Search Collection": "搜索内容", "Search Filters": "搜索过滤器", "search for tags": "搜索标签", "Search Functions": "搜索函数", "Search Knowledge": "搜索知识", "Search Models": "搜索模型", "Search options": "搜索选项", "Search Prompts": "搜索提示词", "Search Result Count": "搜索结果数量", "Search the internet": "联网搜索", "Search Tools": "搜索工具", "SearchApi API Key": "SearchApi API 密钥", "SearchApi Engine": "SearchApi 引擎", "Searched {{count}} sites": "已搜索 {{count}} 个网站", "Searching \"{{searchQuery}}\"": "搜索 \"{{searchQuery}}\" 中", "Searching Knowledge for \"{{searchQuery}}\"": "检索有关 \"{{searchQuery}}\" 的知识中", "Searxng Query URL": "Searxng 查询 URL", "See readme.md for instructions": "查看 readme.md 以获取说明", "See what's new": "查阅最新更新内容", "Seed": "种子 (Seed)", "Select a base model": "选择一个基础模型", "Select a engine": "选择一个搜索引擎", "Select a function": "选择一个函数", "Select a group": "选择一个权限组", "Select a model": "选择一个模型", "Select a pipeline": "选择一个管道", "Select a pipeline url": "选择一个管道 URL", "Select a tool": "选择一个工具", "Select an auth method": "选择身份验证方式", "Select an Ollama instance": "选择一个 Ollama 实例。", "Select Engine": "选择引擎", "Select Knowledge": "选择知识", "Select only one model to call": "请仅选择一个模型来呼叫", "Selected model(s) do not support image inputs": "已选择的模型不支持发送图像", "Semantic distance to query": "语义距离查询", "Send": "发送", "Send a Message": "输入消息", "Send message": "发送消息", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "在请求中发送 `stream_options: { include_usage: true }`。设置后，支持的供应商会在响应中返回 Token 使用信息。", "September": "九月", "SerpApi API Key": "SerpApi API 密钥", "SerpApi Engine": "SerpApi 引擎", "Serper API Key": "Serper API 密钥", "Serply API Key": "Serply API 密钥", "Serpstack API Key": "Serpstack API 密钥", "Server connection verified": "已验证服务器连接", "Set as default": "设为默认", "Set CFG Scale": "设置 CFG Scale", "Set Default Model": "设置默认模型", "Set embedding model": "设置语义向量模型", "Set embedding model (e.g. {{model}})": "设置语义向量模型 (例如：{{model}})", "Set Image Size": "设置图片分辨率", "Set reranking model (e.g. {{model}})": "设置重排模型 (例如：{{model}})", "Set Sampler": "设置 Sampler", "Set Scheduler": "设置 Scheduler", "Set Steps": "设置步骤", "Set Task Model": "设置任务模型", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "设置将加载到 GPU 的层数。增加此值可以显著提高对 GPU 优化的模型的性能，但也可能增加功耗和使用更多 GPU 资源。", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "设置用于计算的工作线程数量。该选项可控制并发处理传入请求的线程数量。增加该值可以提高高并发工作负载下的性能，但也可能消耗更多的 CPU 资源。", "Set Voice": "设置音色", "Set whisper model": "设置 whisper 模型", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "对至少出现过一次的标记设置固定偏置值。较高的值（例如1.5）将更强烈地惩罚重复，而较低的值（例如0.9）则更为宽松。当值为0时，此功能将被禁用。", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "根据标记出现的次数，设置一个缩放偏置值来惩罚重复。较高的值（例如1.5）将更强烈地惩罚重复，而较低的值（例如0.9）则更为宽松。当值为0时，此功能将被禁用。", "Sets how far back for the model to look back to prevent repetition.": "设置模型回溯的范围，以防止重复。", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "设置用于生成的随机数种子。将其设置为特定数字将使模型针对同一提示生成相同的文本。", "Sets the size of the context window used to generate the next token.": "设置用于生成下一个 Token 的上下文窗口的大小。", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "设置要使用的停止序列。遇到这种模式时，大语言模型将停止生成文本并返回。可以通过在模型文件中指定多个单独的停止参数来设置多个停止模式。", "Settings": "设置", "Settings saved successfully!": "设置已成功保存!", "Share": "分享", "Share Chat": "分享对话", "Share to Open WebUI Community": "分享到 OpenWebUI 社区", "Sharing Permissions": "共享权限", "Show": "显示", "Show \"What's New\" modal on login": "在登录时显示“更新内容”弹窗", "Show Admin Details in Account Pending Overlay": "在用户待激活界面中显示管理员邮箱等详细信息", "Show Model": "显示模型", "Show shortcuts": "显示快捷方式", "Show your support!": "表达你的支持！", "Showcased creativity": "很有创意", "Sign in": "登录", "Sign in to {{WEBUI_NAME}}": "登录 {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "使用 LDAP 登录 {{WEBUI_NAME}}", "Sign Out": "登出", "Sign up": "注册", "Sign up to {{WEBUI_NAME}}": "注册 {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "正在登录 {{WEBUI_NAME}}", "sk-1234": "sk-1234", "Sougou Search API sID": "搜狗搜索 API 的 Secret ID", "Sougou Search API SK": "搜狗搜索 API 的 Secret Key", "Source": "来源", "Speech Playback Speed": "语音播放速度", "Speech recognition error: {{error}}": "语音识别错误：{{error}}", "Speech-to-Text Engine": "语音转文本引擎", "Stop": "停止", "Stop Sequence": "停止序列 (Stop Sequence)", "Stream Chat Response": "以流式返回对话响应", "STT Model": "语音转文本模型", "STT Settings": "语音转文本设置", "Subtitle (e.g. about the Roman Empire)": "副标题（例如：关于罗马帝国的副标题）", "Success": "成功", "Successfully updated.": "成功更新。", "Suggested": "建议", "Support": "支持", "Support this plugin:": "支持此插件", "Sync directory": "同步目录", "System": "系统", "System Instructions": "系统指令", "System Prompt": "系统提示词 (System Prompt)", "Tags": "标签", "Tags Generation": "标签生成", "Tags Generation Prompt": "标签生成提示词", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "无尾采样用于减少输出中出现概率较小的 Token 的影响。较高的值（例如 2.0）将进一步减少影响，而值 1.0 则禁用此设置。", "Talk to model": "与模型交谈", "Tap to interrupt": "点击以中断", "Tasks": "任务", "Tavily API Key": "Tavily API 密钥", "Tavily Extract Depth": "Tavily 提取深度", "Tell us more:": "请告诉我们更多细节", "Temperature": "温度 (Temperature)", "Template": "模板", "Temporary Chat": "临时对话", "Text Splitter": "文本分切器", "Text-to-Speech Engine": "文本转语音引擎", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "感谢您的反馈！", "The Application Account DN you bind with for search": "您所绑定用于搜索的 Application Account DN", "The base to search for users": "搜索用户的 Base", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "批处理大小决定了一次可以处理多少个文本请求。更高的批处理大小可以提高模型的性能和速度，但也需要更多内存。", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "本插件的背后开发者是社区中热情的志愿者。如果此插件有帮助到您，烦请考虑一下为它的开发做出贡献。", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "排行榜基于 Elo 评级系统并实时更新。", "The LDAP attribute that maps to the mail that users use to sign in.": "映射到用户登录时使用的邮箱的 LDAP 属性。", "The LDAP attribute that maps to the username that users use to sign in.": "映射到用户登录时使用的用户名的 LDAP 属性。", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "排行榜目前处于 Beta 测试阶段，我们可能会在完善算法后调整评分计算方法。", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "最大文件大小（MB）。如果文件大小超过此限制，则无法上传该文件。", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "在单次对话中可以使用的最大文件数。如果文件数超过此限制，则文件不会上传。", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "分值应介于 0.0（0%）和 1.0（100%）之间。", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "模型的温度。增加温度将使模型的回答更有创意。", "Theme": "主题", "Thinking...": "正在思考...", "This action cannot be undone. Do you wish to continue?": "此操作无法撤销。是否确认继续？", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "此频道创建于{{createdAt}}，这里是{{channelName}}频道的开始", "This chat won’t appear in history and your messages will not be saved.": "此聊天不会出现在历史记录中，且您的消息不会被保存。", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "这将确保您的宝贵对话被安全地保存到后台数据库中。感谢！", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "这是一个实验功能，可能不会如预期那样工作，而且可能随时发生变化。", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "此选项控制刷新上下文时保留多少 Token。例如，如果设置为 2，则将保留对话上下文的最后 2 个 Token。保留上下文有助于保持对话的连续性，但可能会降低响应新主题的能力。", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "此项用于设置模型在其响应中可以生成的最大 Token 数。增加此限制可让模型提供更长的答案，但也可能增加生成无用或不相关内容的可能性。", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "此选项将会删除文件集中所有文件，并用新上传的文件替换。", "This response was generated by \"{{model}}\"": "此回复由 \"{{model}}\" 生成", "This will delete": "这将删除", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "这将删除<strong>{{NAME}}</strong>及其<strong>所有内容</strong>。", "This will delete all models including custom models": "这将删除所有模型，包括自定义模型", "This will delete all models including custom models and cannot be undone.": "这将删除所有模型，包括自定义模型，且无法撤销。", "This will reset the knowledge base and sync all files. Do you wish to continue?": "这将重置知识库并替换所有文件为目录下文件。确认继续？", "Thorough explanation": "解释较为详细", "Thought for {{DURATION}}": "思考用时 {{DURATION}}", "Thought for {{DURATION}} seconds": "思考用时 {{DURATION}} 秒", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "请输入 Tika 服务器地址。", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "提示：在每次替换后，在对话输入中按 Tab 键可以连续更新多个变量。", "Title": "标题", "Title (e.g. Tell me a fun fact)": "标题（例如：给我讲一个有趣的事实）", "Title Auto-Generation": "自动生成标题", "Title cannot be an empty string.": "标题不能为空。", "Title Generation": "标题生成", "Title Generation Prompt": "用于自动生成标题的提示词", "TLS": "传输层安全协议", "To access the available model names for downloading,": "要访问可下载的模型名称，", "To access the GGUF models available for downloading,": "要访问可下载的 GGUF 模型，", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "请联系管理员以访问。管理员可以在后台管理面板中管理用户状态。", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "要在这里附加知识库，请先将其添加到工作空间中的“知识库”。", "To learn more about available endpoints, visit our documentation.": "要了解有关可用端点的更多信息，请访问我们的文档。", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "为了保护您的隐私，只有评分、模型ID、标签和元数据会从您的反馈中分享——您的聊天记录将保持私密，不会被包含在内。", "To select actions here, add them to the \"Functions\" workspace first.": "要在这里选择自动化，请先将其添加到工作空间中的“函数”。", "To select filters here, add them to the \"Functions\" workspace first.": "要在这里选择过滤器，请先将其添加到工作空间中的“函数”。", "To select toolkits here, add them to the \"Tools\" workspace first.": "要在这里选择工具包，请先将其添加到工作空间中的“工具”。", "Toast notifications for new updates": "更新后弹窗提示更新内容", "Today": "今天", "Toggle settings": "切换设置", "Toggle sidebar": "切换侧边栏", "Token": "Token", "Tokens To Keep On Context Refresh (num_keep)": "在语境刷新时需保留的 Token 数量", "Too verbose": "过于冗长", "Tool created successfully": "工具创建成功", "Tool deleted successfully": "工具删除成功", "Tool Description": "工具描述", "Tool ID": "工具 ID", "Tool imported successfully": "工具导入成功", "Tool Name": "工具名称", "Tool Servers": "工具服务器", "Tool updated successfully": "工具更新成功", "Tools": "工具", "Tools Access": "访问工具", "Tools are a function calling system with arbitrary code execution": "工具是一个具有任意代码执行能力的函数调用系统", "Tools Function Calling Prompt": "工具函数调用提示词", "Tools have a function calling system that allows arbitrary code execution": "注意：工具有权执行任意代码", "Tools have a function calling system that allows arbitrary code execution.": "注意：工具有权执行任意代码。", "Tools Public Sharing": "工具公开分享", "Top K": "Top K", "Top K Reranker": "Top K Reranker", "Top P": "Top P", "Transformers": "Transformers", "Trouble accessing Ollama?": "访问 Ollama 时遇到问题？", "Trust Proxy Environment": "信任代理环境", "TTS Model": "文本转语音模型", "TTS Settings": "文本转语音设置", "TTS Voice": "文本转语音音色", "Type": "类型", "Type Hugging Face Resolve (Download) URL": "输入 Hugging Face 解析（下载）URL", "Uh-oh! There was an issue with the response.": "啊哦！回复有问题。", "UI": "界面", "Unarchive All": "取消所有存档", "Unarchive All Archived Chats": "取消所有已存档的对话", "Unarchive Chat": "取消存档当前对话", "Unlock mysteries": "揭开神秘面纱", "Unpin": "取消置顶", "Unravel secrets": "解开秘密", "Untagged": "无标签", "Update": "更新", "Update and Copy Link": "更新和复制链接", "Update for the latest features and improvements.": "更新来获得最新功能与改进。", "Update password": "更新密码", "Updated": "已更新", "Updated at": "更新于", "Updated At": "更新于", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "升级到授权计划以获得增强功能，包括自定义主题与品牌以及专属支持。", "Upload": "上传", "Upload a GGUF model": "上传一个 GGUF 模型", "Upload directory": "上传目录", "Upload files": "上传文件", "Upload Files": "上传文件", "Upload Pipeline": "上传 Pipeline", "Upload Progress": "上传进度", "URL": "URL", "URL Mode": "URL 模式", "Use '#' in the prompt input to load and include your knowledge.": "在输入框中输入 '#' 号来加载你需要的知识库内容。", "Use Gravatar": "使用来自 Gravatar 的头像", "Use groups to group your users and assign permissions.": "使用权限组来组织用户并分配权限。", "Use Initials": "使用首个字符作为头像", "Use no proxy to fetch page contents.": "不使用代理获取页面内容。", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "使用由 http_proxy 和 https_proxy 环境变量指定的代理获取页面内容。", "use_mlock (Ollama)": "use_mlock（Ollama）", "use_mmap (Ollama)": "use_mmap （Ollama）", "user": "用户", "User": "用户", "User location successfully retrieved.": "成功检索到用户位置。", "User Webhooks": "用户 Webhook", "Username": "用户名", "Users": "用户", "Using the default arena model with all models. Click the plus button to add custom models.": "竞技场模型默认使用所有模型。单击加号按钮添加自定义模型。", "Utilize": "利用", "Valid time units:": "有效时间单位：", "Valves": "值", "Valves updated": "已更新值", "Valves updated successfully": "值更新成功", "variable": "变量", "variable to have them replaced with clipboard content.": "变量将被剪贴板内容替换。", "Verify Connection": "验证连接", "Verify SSL Certificate": "", "Version": "版本", "Version {{selectedVersion}} of {{totalVersions}}": "版本 {{selectedVersion}}/{{totalVersions}}", "View Replies": "查看回复", "View Result from **{{NAME}}**": "查看来自 **{{NAME}}** 的结果", "Visibility": "可见性", "Voice": "语音", "Voice Input": "语音输入", "Warning": "警告", "Warning:": "警告：", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "警告：启用此功能将允许用户在服务器上上传任意代码。", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "警告：如果您修改了语义向量模型，则需要重新导入所有文档。", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "警告：Jupyter 执行允许任意代码执行，存在严重的安全风险——请极其谨慎地操作。", "Web": "网页", "Web API": "网页 API", "Web Loader Engine": "网页加载引擎", "Web Search": "联网搜索", "Web Search Engine": "联网搜索引擎", "Web Search in Chat": "聊天中的网页搜索", "Web Search Query Generation": "网页搜索关键词生成", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI 设置", "WebUI URL": "WebUI URL", "WebUI will make requests to \"{{url}}\"": "WebUI 将向 \"{{url}}\" 发出请求", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI 将向 \"{{url}}/api/chat\" 发出请求", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI 将向 \"{{url}}/chat/completions\" 发出请求", "What are you trying to achieve?": "你想要达到什么目标？", "What are you working on?": "你在忙于什么？", "What’s New in": "最近更新内容于", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "启用后，模型将实时回复每条聊天信息，在用户发送信息后立即生成回复。这种模式对即时聊天应用非常有用，但可能会影响较慢硬件的性能。", "wherever you are": "无论你在哪里", "Whisper (Local)": "<PERSON>hisper (本地)", "Why?": "为什么？", "Widescreen Mode": "宽屏模式", "Won": "获胜", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "与 top-k 配合使用。较高的值（例如 0.95）将产生更加多样化的文本，而较低的值（例如 0.5）将产生更加集中和保守的文本。", "Workspace": "工作空间", "Workspace Permissions": "工作空间权限", "Write": "写作", "Write a prompt suggestion (e.g. Who are you?)": "写一个提示词建议（例如：你是谁？）", "Write a summary in 50 words that summarizes [topic or keyword].": "用 50 个字写一个总结 [主题或关键词]。", "Write something...": "单击以键入内容...", "Write your model template content here": "在此写入模型模板内容", "Yesterday": "昨天", "You": "你", "You are currently using a trial license. Please contact support to upgrade your license.": "当前为试用许可证，请联系支持人员升级许可证。", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "每次对话最多仅能附上 {{maxCount}} 个文件。", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "通过点击下方的“管理”按钮，你可以添加记忆，以个性化大语言模型的互动，使其更有用，更符合你的需求。", "You cannot upload an empty file.": "请勿上传空文件。", "You do not have permission to upload files": "你没有上传文件的权限", "You do not have permission to upload files.": "你没有上传文件的权限。", "You have no archived conversations.": "没有已归档的对话。", "You have shared this chat": "此对话已经分享过", "You're a helpful assistant.": "你是一个有帮助的助手。", "You're now logged in.": "已登录。", "Your account status is currently pending activation.": "您的账号当前状态为待激活。", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "您的全部捐款将直接给到插件开发者，Open WebUI 不会收取任何比例。但众筹平台可能会有服务费、抽成。", "Youtube": "YouTube", "Youtube Language": "Youtube 语言", "Youtube Proxy URL": "Youtube 代理 URL"}
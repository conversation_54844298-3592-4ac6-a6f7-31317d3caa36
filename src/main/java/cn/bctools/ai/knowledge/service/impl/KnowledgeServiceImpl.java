// 创建 SpEL 上下文
StandardEvaluationContext context = new StandardEvaluationContext();
// 添加类型定位器（用于识别 T(...) 语法）
context.setTypeLocator(new StandardTypeLocator(ClassLoader.getSystemClassLoader()));
String a = "批量#{T(cn.bctools.ai.knowledge.entity.enums.StatusAction).valueOf(#_path['action'].toUpperCase()).getDesc()}文档";
Map<String, String> map = Map.of("action", "enable");
context.setVariable("_path", map);
// 解析模板表达式
ExpressionParser parser = new SpelExpressionParser();
Expression expression = parser.parseExpression(a, new TemplateParserContext());
String value = expression.getValue(context, String.class);
System.out.println(value); 
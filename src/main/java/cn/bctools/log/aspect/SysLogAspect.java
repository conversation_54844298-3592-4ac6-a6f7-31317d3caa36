package cn.bctools.log.aspect;

import cn.bctools.common.entity.dto.UserInfoDto;
import cn.bctools.common.exception.BusinessException;
import cn.bctools.common.utils.ObjectNull;
import cn.bctools.common.utils.R;
import cn.bctools.common.utils.SpringContextUtil;
import cn.bctools.common.utils.StackTraceElementUtils;
import cn.bctools.common.utils.SystemThreadLocal;
import cn.bctools.common.utils.TenantContextHolder;
import cn.bctools.log.annotation.Log;
import cn.bctools.log.annotation.LogCallBack;
import cn.bctools.log.annotation.LogIgnore;
import cn.bctools.log.config.LogMqConfig;
import cn.bctools.log.po.LogPo;
import cn.bctools.log.utils.IpUtils;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.validation.support.BindingAwareModelMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.cloud.sleuth.Tracer;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 注解日志拦截处理，将所有的拦截日志和业务日志通过注入的Bean保存到数据库中
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Configuration
@Order(1)
public class SysLogAspect {

    private static final long LONG_EXECUTION_THRESHOLD_MS = 5000L;
    private static final int BUSINESS_EXCEPTION_ERROR_CODE = 20000;
    private static final int CORE_POOL_SIZE = 1;
    private static final int MAX_POOL_SIZE = 10;
    private static final int QUEUE_CAPACITY = 1_000_000;
    private static final long KEEP_ALIVE_TIME = 60L;
    private static final long SHUTDOWN_TIMEOUT = 30L;

    @Value("${log.close:false}")
    private boolean logClose;

    private final Tracer tracer;
    private final RabbitTemplate rabbitTemplate;
    private final ApplicationContext applicationContext;
    private final LogMqConfig logMqConfig;
    private ExecutorService logExecutor;

    public SysLogAspect(Tracer tracer, RabbitTemplate rabbitTemplate, 
                       ApplicationContext applicationContext, LogMqConfig logMqConfig) {
        this.tracer = tracer;
        this.rabbitTemplate = rabbitTemplate;
        this.applicationContext = applicationContext;
        this.logMqConfig = logMqConfig;
    }

    @PostConstruct
    public void init() {
        logExecutor = new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(QUEUE_CAPACITY),
                new ThreadPoolExecutor.DiscardPolicy()
        );
    }

    @PreDestroy
    public void destroy() {
        if (logExecutor != null && !logExecutor.isShutdown()) {
            logExecutor.shutdown();
            try {
                if (!logExecutor.awaitTermination(SHUTDOWN_TIMEOUT, TimeUnit.SECONDS)) {
                    log.warn("Log executor did not terminate in the specified time.");
                    logExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.warn("Log executor shutdown interrupted.", e);
                Thread.currentThread().interrupt();
                logExecutor.shutdownNow();
            }
        }
    }

    @Around("@annotation(logannotation)")
    public Object around(ProceedingJoinPoint point, Log logannotation) throws Throwable {
        if (logClose) {
            return point.proceed();
        }

        LogPo logPo = new LogPo();
        LocalDateTime startTime = LocalDateTime.now();

        try {
            initBaseLogInfo(point, logannotation, logPo, startTime);
            UserInfoDto currentUserInfo = SystemThreadLocal.get("user");
            if (ObjectNull.isNotNull(currentUserInfo)) {
                populateUserInfo(logPo, currentUserInfo);
            }

            Object result = point.proceed();
            handleSuccess(logannotation, logPo, currentUserInfo);
            return result;

        } catch (Throwable throwable) {
            handleException(logPo, throwable);
            throw throwable;
        } finally {
            finalizeLog(logannotation, logPo, startTime);
        }
    }

    private void initBaseLogInfo(ProceedingJoinPoint point, Log logannotation, LogPo logPo, LocalDateTime startTime) {
        IpUtils.getIpAddr(logPo);

        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        Method method = methodSignature.getMethod();

        String traceId = Optional.ofNullable(tracer.currentSpan())
                .map(span -> span.context().traceId())
                .orElse("--");

        logPo.setStatus(true)
                .setBusinessName(applicationContext.getApplicationName())
                .setStartTime(startTime)
                .setClassName(point.getTarget().getClass().getName())
                .setMethodName(method.getName())
                .setEnv(SpringContextUtil.getEnv())
                .setVersion(SpringContextUtil.getVersion())
                .setTenantId(TenantContextHolder.getTenantId())
                .setCreateDate(startTime)
                .setTid(traceId);

        setFunctionNameAndOperationType(logannotation, logPo, method, point.getTarget().getClass());
        initParameters(point, logannotation, logPo, method);
    }

    private void populateUserInfo(LogPo logPo, UserInfoDto userInfo) {
        logPo.setUserName(userInfo.getUserDto().getRealName())
             .setUserId(userInfo.getUserDto().getId())
             .setClientId(userInfo.getUserDto().getClientId())
             .setClientName(userInfo.getUserDto().getClientName());

        if (ObjectNull.isNotEmpty(userInfo.getUserDto().getIp())) {
            logPo.setIp(userInfo.getUserDto().getIp());
        }
    }

    private void handleSuccess(Log logannotation, LogPo logPo, UserInfoDto currentUserInfo) {
        Optional.ofNullable(SystemThreadLocal.get("functionName"))
                .map(Object::toString)
                .ifPresent(logPo::setFunctionName);

        if (!logannotation.callBackClass().equals(LogCallBack.class)) {
            try {
                LogCallBack callBackBean = applicationContext.getBean(logannotation.callBackClass());
                if (ObjectNull.isNotNull(currentUserInfo)) {
                    callBackBean.callBack(
                            currentUserInfo.getUserDto().getRealName(),
                            logPo.getFunctionName(),
                            JSONObject.toJSONString(logPo.getParameters())
                    );
                }
            } catch (Exception e) {
                log.error("LogCallBack execution failed for class: {}", logannotation.callBackClass().getName(), e);
            }
        }
    }

    private void handleException(LogPo logPo, Throwable throwable) {
        logPo.setStatus(false);

        if (throwable instanceof BusinessException) {
            BusinessException businessException = (BusinessException) throwable;
            logPo.setExceptionMessage(businessException.getMessage());
            
            if (businessException.getCode() != BUSINESS_EXCEPTION_ERROR_CODE) {
                log.error("AOP caught BusinessException (code: {}) for function: {}", 
                        businessException.getCode(), logPo.getFunctionName(), throwable);
            } else {
                log.warn("AOP caught expected BusinessException (code: {}) for function: {}", 
                        businessException.getCode(), logPo.getFunctionName());
            }
        } else {
            log.error("AOP caught unexpected error for function: {}", logPo.getFunctionName(), throwable);
            logPo.setExceptionMessage(throwable.getMessage())
                 .setElements(StackTraceElementUtils.logThrowableToString(throwable));
        }
    }

    private void finalizeLog(Log logannotation, LogPo logPo, LocalDateTime startTime) {
        LocalDateTime endTime = LocalDateTime.now();
        long executionTime = Duration.between(startTime, endTime).toMillis();
        
        logPo.setEndTime(endTime)
             .setConsumingTime(executionTime);

        if (executionTime > LONG_EXECUTION_THRESHOLD_MS) {
            log.warn("Long execution detected for function: {}, time: {}ms", 
                    logPo.getFunctionName(), executionTime);
        }

        // 异步保存日志
        logExecutor.execute(() -> {
            try {
                rabbitTemplate.convertAndSend(
                        logMqConfig.getExchange(),
                        logMqConfig.getRoutingKey(),
                        logPo
                );
            } catch (Exception e) {
                log.error("Failed to send log to RabbitMQ", e);
            }
        });
    }

    private void setFunctionNameAndOperationType(Log logannotation, LogPo logPo, Method method, Class<?> targetClass) {
        // 实现设置功能名称和操作类型的逻辑
    }

    private void initParameters(ProceedingJoinPoint point, Log logannotation, LogPo logPo, Method method) {
        // 实现参数初始化的逻辑
    }
} 
{"version": 3, "sources": ["../src/js/pyodide.umd.ts", "../src/js/vendor/stackframe/stackframe.ts", "../src/js/vendor/stackframe/error-stack-parser.ts", "../src/js/environments.ts", "../src/js/compat.ts", "../src/js/nativefs.ts", "../src/js/emscripten-settings.ts", "../src/js/version.ts", "../src/js/pyodide.ts"], "sourcesContent": ["import { loadPyodide, version } from \"./pyodide\";\nimport { type PackageData } from \"./types\";\nexport { loadPyodide, version, type PackageData };\n(globalThis as any).loadPyodide = loadPyodide;\n", "// @ts-nocheck\n// Port of https://github.com/stacktracejs/stackframe/blob/master/stackframe.js.\n// Rewritten to ES6 and removed UMD and CommonJS support.\n\nfunction _isNumber(n) {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n}\n\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.substring(1);\n}\n\nfunction _getter(p) {\n  return function () {\n    return this[p];\n  };\n}\n\nvar booleanProps = [\"isConstructor\", \"isEval\", \"isNative\", \"isToplevel\"];\nvar numericProps = [\"columnNumber\", \"lineNumber\"];\nvar stringProps = [\"fileName\", \"functionName\", \"source\"];\nvar arrayProps = [\"args\"];\nvar objectProps = [\"evalOrigin\"];\n\nvar props = booleanProps.concat(\n  numericProps,\n  stringProps,\n  arrayProps,\n  objectProps,\n);\n\ndeclare namespace StackFrame {\n  export interface StackFrameOptions {\n    isConstructor?: boolean;\n    isEval?: boolean;\n    isNative?: boolean;\n    isToplevel?: boolean;\n    columnNumber?: number;\n    lineNumber?: number;\n    fileName?: string;\n    functionName?: string;\n    source?: string;\n    args?: any[];\n    evalOrigin?: StackFrame;\n  }\n}\n\ndeclare class StackFrame {\n  constructor(obj: StackFrame.StackFrameOptions);\n\n  args?: any[];\n  getArgs(): any[] | undefined;\n  setArgs(args: any[]): void;\n\n  evalOrigin?: StackFrame;\n  getEvalOrigin(): StackFrame | undefined;\n  setEvalOrigin(stackframe: StackFrame): void;\n\n  isConstructor?: boolean;\n  getIsConstructor(): boolean | undefined;\n  setIsConstructor(isConstructor: boolean): void;\n\n  isEval?: boolean;\n  getIsEval(): boolean | undefined;\n  setIsEval(isEval: boolean): void;\n\n  isNative?: boolean;\n  getIsNative(): boolean | undefined;\n  setIsNative(isNative: boolean): void;\n\n  isToplevel?: boolean;\n  getIsToplevel(): boolean | undefined;\n  setIsToplevel(isToplevel: boolean): void;\n\n  columnNumber?: number;\n  getColumnNumber(): number | undefined;\n  setColumnNumber(columnNumber: number): void;\n\n  lineNumber?: number;\n  getLineNumber(): number | undefined;\n  setLineNumber(lineNumber: number): void;\n\n  fileName?: string;\n  getFileName(): string | undefined;\n  setFileName(fileName: string): void;\n\n  functionName?: string;\n  getFunctionName(): string | undefined;\n  setFunctionName(functionName: string): void;\n\n  source?: string;\n  getSource(): string | undefined;\n  setSource(source: string): void;\n\n  toString(): string;\n}\n\nfunction StackFrame(obj) {\n  if (!obj) return;\n  for (var i = 0; i < props.length; i++) {\n    if (obj[props[i]] !== undefined) {\n      this[\"set\" + _capitalize(props[i])](obj[props[i]]);\n    }\n  }\n}\n\nStackFrame.prototype = {\n  getArgs: function () {\n    return this.args;\n  },\n  setArgs: function (v) {\n    if (Object.prototype.toString.call(v) !== \"[object Array]\") {\n      throw new TypeError(\"Args must be an Array\");\n    }\n    this.args = v;\n  },\n\n  getEvalOrigin: function () {\n    return this.evalOrigin;\n  },\n  setEvalOrigin: function (v) {\n    if (v instanceof StackFrame) {\n      this.evalOrigin = v;\n    } else if (v instanceof Object) {\n      this.evalOrigin = new StackFrame(v);\n    } else {\n      throw new TypeError(\"Eval Origin must be an Object or StackFrame\");\n    }\n  },\n\n  toString: function () {\n    var fileName = this.getFileName() || \"\";\n    var lineNumber = this.getLineNumber() || \"\";\n    var columnNumber = this.getColumnNumber() || \"\";\n    var functionName = this.getFunctionName() || \"\";\n    if (this.getIsEval()) {\n      if (fileName) {\n        return (\n          \"[eval] (\" + fileName + \":\" + lineNumber + \":\" + columnNumber + \")\"\n        );\n      }\n      return \"[eval]:\" + lineNumber + \":\" + columnNumber;\n    }\n    if (functionName) {\n      return (\n        functionName +\n        \" (\" +\n        fileName +\n        \":\" +\n        lineNumber +\n        \":\" +\n        columnNumber +\n        \")\"\n      );\n    }\n    return fileName + \":\" + lineNumber + \":\" + columnNumber;\n  },\n};\n\nStackFrame.fromString = function StackFrame$$fromString(str) {\n  var argsStartIndex = str.indexOf(\"(\");\n  var argsEndIndex = str.lastIndexOf(\")\");\n\n  var functionName = str.substring(0, argsStartIndex);\n  var args = str.substring(argsStartIndex + 1, argsEndIndex).split(\",\");\n  var locationString = str.substring(argsEndIndex + 1);\n\n  if (locationString.indexOf(\"@\") === 0) {\n    var parts = /@(.+?)(?::(\\d+))?(?::(\\d+))?$/.exec(locationString, \"\");\n    var fileName = parts[1];\n    var lineNumber = parts[2];\n    var columnNumber = parts[3];\n  }\n\n  return new StackFrame({\n    functionName: functionName,\n    args: args || undefined,\n    fileName: fileName,\n    lineNumber: lineNumber || undefined,\n    columnNumber: columnNumber || undefined,\n  });\n};\n\nfor (var i = 0; i < booleanProps.length; i++) {\n  StackFrame.prototype[\"get\" + _capitalize(booleanProps[i])] = _getter(\n    booleanProps[i],\n  );\n  StackFrame.prototype[\"set\" + _capitalize(booleanProps[i])] = (function (p) {\n    return function (v) {\n      this[p] = Boolean(v);\n    };\n  })(booleanProps[i]);\n}\n\nfor (var j = 0; j < numericProps.length; j++) {\n  StackFrame.prototype[\"get\" + _capitalize(numericProps[j])] = _getter(\n    numericProps[j],\n  );\n  StackFrame.prototype[\"set\" + _capitalize(numericProps[j])] = (function (p) {\n    return function (v) {\n      if (!_isNumber(v)) {\n        throw new TypeError(p + \" must be a Number\");\n      }\n      this[p] = Number(v);\n    };\n  })(numericProps[j]);\n}\n\nfor (var k = 0; k < stringProps.length; k++) {\n  StackFrame.prototype[\"get\" + _capitalize(stringProps[k])] = _getter(\n    stringProps[k],\n  );\n  StackFrame.prototype[\"set\" + _capitalize(stringProps[k])] = (function (p) {\n    return function (v) {\n      this[p] = String(v);\n    };\n  })(stringProps[k]);\n}\n\nexport default StackFrame;\n", "// @ts-nocheck\n// Port of https://github.com/stacktracejs/error-stack-parser\n// Rewritten to ES6 and removed UMD and CommonJS support.\n// Removed old opera support.\n\nimport StackFrame from \"./stackframe\";\n\ndeclare namespace ErrorStackParser {\n  export type { StackFrame };\n  /**\n   * Given an Error object, extract the most information from it.\n   *\n   * @param {Error} error object\n   * @return {Array} of StackFrames\n   */\n  export function parse(error: Error): StackFrame[];\n}\n\nfunction ErrorStackParser() {\n  var CHROME_IE_STACK_REGEXP = /^\\s*at .*(\\S+:\\d+|\\(native\\))/m;\n  var SAFARI_NATIVE_CODE_REGEXP = /^(eval@)?(\\[native code])?$/;\n\n  return {\n    /**\n     * Given an Error object, extract the most information from it.\n     *\n     * @param {Error} error object\n     * @return {Array} of StackFrames\n     */\n    parse: function ErrorStackParser$$parse(error: Error): StackFrame[] {\n      if (error.stack && error.stack.match(CHROME_IE_STACK_REGEXP)) {\n        return this.parseV8OrIE(error);\n      } else if (error.stack) {\n        return this.parseFFOrSafari(error);\n      } else {\n        throw new Error(\"Cannot parse given Error object\");\n      }\n    },\n\n    // Separate line and column numbers from a string of the form: (URI:Line:Column)\n    extractLocation: function ErrorStackParser$$extractLocation(urlLike) {\n      // Fail-fast but return locations like \"(native)\"\n      if (urlLike.indexOf(\":\") === -1) {\n        return [urlLike];\n      }\n\n      var regExp = /(.+?)(?::(\\d+))?(?::(\\d+))?$/;\n      var parts = regExp.exec(urlLike.replace(/[()]/g, \"\"));\n      return [parts[1], parts[2] || undefined, parts[3] || undefined];\n    },\n\n    parseV8OrIE: function ErrorStackParser$$parseV8OrIE(error) {\n      var filtered = error.stack.split(\"\\n\").filter(function (line) {\n        return !!line.match(CHROME_IE_STACK_REGEXP);\n      }, this);\n\n      return filtered.map(function (line) {\n        if (line.indexOf(\"(eval \") > -1) {\n          // Throw away eval information until we implement stacktrace.js/stackframe#8\n          line = line\n            .replace(/eval code/g, \"eval\")\n            .replace(/(\\(eval at [^()]*)|(,.*$)/g, \"\");\n        }\n        var sanitizedLine = line\n          .replace(/^\\s+/, \"\")\n          .replace(/\\(eval code/g, \"(\")\n          .replace(/^.*?\\s+/, \"\");\n\n        // capture and preserve the parenthesized location \"(/foo/my bar.js:12:87)\" in\n        // case it has spaces in it, as the string is split on \\s+ later on\n        var location = sanitizedLine.match(/ (\\(.+\\)$)/);\n\n        // remove the parenthesized location from the line, if it was matched\n        sanitizedLine = location\n          ? sanitizedLine.replace(location[0], \"\")\n          : sanitizedLine;\n\n        // if a location was matched, pass it to extractLocation() otherwise pass all sanitizedLine\n        // because this line doesn't have function name\n        var locationParts = this.extractLocation(\n          location ? location[1] : sanitizedLine,\n        );\n        var functionName = (location && sanitizedLine) || undefined;\n        var fileName =\n          [\"eval\", \"<anonymous>\"].indexOf(locationParts[0]) > -1\n            ? undefined\n            : locationParts[0];\n\n        return new StackFrame({\n          functionName: functionName,\n          fileName: fileName,\n          lineNumber: locationParts[1],\n          columnNumber: locationParts[2],\n          source: line,\n        });\n      }, this);\n    },\n\n    parseFFOrSafari: function ErrorStackParser$$parseFFOrSafari(error) {\n      var filtered = error.stack.split(\"\\n\").filter(function (line) {\n        return !line.match(SAFARI_NATIVE_CODE_REGEXP);\n      }, this);\n\n      return filtered.map(function (line) {\n        // Throw away eval information until we implement stacktrace.js/stackframe#8\n        if (line.indexOf(\" > eval\") > -1) {\n          line = line.replace(\n            / line (\\d+)(?: > eval line \\d+)* > eval:\\d+:\\d+/g,\n            \":$1\",\n          );\n        }\n\n        if (line.indexOf(\"@\") === -1 && line.indexOf(\":\") === -1) {\n          // Safari eval frames only have function names and nothing else\n          return new StackFrame({\n            functionName: line,\n          });\n        } else {\n          var functionNameRegex = /((.*\".+\"[^@]*)?[^@]*)(?:@)/;\n          var matches = line.match(functionNameRegex);\n          var functionName = matches && matches[1] ? matches[1] : undefined;\n          var locationParts = this.extractLocation(\n            line.replace(functionNameRegex, \"\"),\n          );\n\n          return new StackFrame({\n            functionName: functionName,\n            fileName: locationParts[0],\n            lineNumber: locationParts[1],\n            columnNumber: locationParts[2],\n            source: line,\n          });\n        }\n      }, this);\n    },\n  };\n}\n\nconst errorStackParser = new ErrorStackParser();\n\nexport { StackFrame };\nexport default errorStackParser;\n", "// @ts-nocheck\n\n/** @private */\nexport const IN_NODE =\n  typeof process === \"object\" &&\n  typeof process.versions === \"object\" &&\n  typeof process.versions.node === \"string\" &&\n  !process.browser; /* This last condition checks if we run the browser shim of process */\n\n/** @private */\nexport const IN_NODE_COMMONJS =\n  IN_NODE &&\n  typeof module !== \"undefined\" &&\n  typeof module.exports !== \"undefined\" &&\n  typeof require !== \"undefined\" &&\n  typeof __dirname !== \"undefined\";\n\n/** @private */\nexport const IN_NODE_ESM = IN_NODE && !IN_NODE_COMMONJS;\n\n/** @private */\nexport const IN_BUN = typeof globalThis.Bun !== \"undefined\";\n\n/** @private */\nexport const IN_DENO = typeof Deno !== \"undefined\"; // just in case...\n\n/** @private */\nexport const IN_BROWSER = !IN_NODE && !IN_DENO;\n\n/** @private */\nexport const IN_BROWSER_MAIN_THREAD =\n  IN_BROWSER &&\n  typeof window === \"object\" &&\n  typeof document === \"object\" &&\n  typeof document.createElement === \"function\" &&\n  \"sessionStorage\" in window &&\n  typeof importScripts !== \"function\";\n\n/** @private */\nexport const IN_BROWSER_WEB_WORKER =\n  IN_BROWSER && typeof importScripts === \"function\" && typeof self === \"object\";\n\n/** @private */\nexport const IN_SAFARI =\n  typeof navigator === \"object\" &&\n  typeof navigator.userAgent === \"string\" &&\n  navigator.userAgent.indexOf(\"Chrome\") == -1 &&\n  navigator.userAgent.indexOf(\"Safari\") > -1;\n\n/**\n * Detects the current environment and returns a record with the results.\n * This function is useful for debugging and testing purposes.\n * @private\n */\nexport function detectEnvironment(): Record<string, boolean> {\n  return {\n    IN_NODE: IN_NODE,\n    IN_NODE_COMMONJS: IN_NODE_COMMONJS,\n    IN_NODE_ESM: IN_NODE_ESM,\n    IN_BUN: IN_BUN,\n    IN_DENO: IN_DENO,\n    IN_BROWSER: IN_BROWSER,\n    IN_BROWSER_MAIN_THREAD: IN_BROWSER_MAIN_THREAD,\n    IN_BROWSER_WEB_WORKER: IN_BROWSER_WEB_WORKER,\n    IN_SAFARI: IN_SAFARI,\n  };\n}\n", "import Error<PERSON>tack<PERSON>arser from \"./vendor/stackframe/error-stack-parser\";\nimport {\n  IN_NODE,\n  IN_NODE_ESM,\n  IN_BROWSER_MAIN_THREAD,\n  IN_BROWSER_WEB_WORKER,\n  IN_NODE_COMMONJS,\n} from \"./environments\";\nimport { Lockfile } from \"./types\";\n\nlet nodeUrlMod: typeof import(\"node:url\");\nlet nodePath: typeof import(\"node:path\");\nlet nodeVmMod: typeof import(\"node:vm\");\n/** @private */\nexport let nodeFSMod: typeof import(\"node:fs\");\n/** @private */\nexport let nodeFsPromisesMod: typeof import(\"node:fs/promises\");\n\ndeclare var globalThis: {\n  importScripts: (url: string) => void;\n  document?: typeof document;\n  fetch?: typeof fetch;\n};\n\n/**\n * If we're in node, it's most convenient to import various node modules on\n * initialization. Otherwise, this does nothing.\n * @private\n */\nexport async function initNodeModules() {\n  if (!IN_NODE) {\n    return;\n  }\n  // @ts-ignore\n  nodeUrlMod = (await import(\"node:url\")).default;\n  nodeFSMod = await import(\"node:fs\");\n  nodeFsPromisesMod = await import(\"node:fs/promises\");\n\n  // @ts-ignore\n  nodeVmMod = (await import(\"node:vm\")).default;\n  nodePath = await import(\"node:path\");\n  pathSep = nodePath.sep;\n\n  // Emscripten uses `require`, so if it's missing (because we were imported as\n  // an ES6 module) we need to polyfill `require` with `import`. `import` is\n  // async and `require` is synchronous, so we import all packages that might be\n  // required up front and define require to look them up in this table.\n\n  if (typeof require !== \"undefined\") {\n    return;\n  }\n  // These are all the packages required in pyodide.asm.js. You can get this\n  // list with:\n  // $ grep -o 'require(\"[a-z]*\")' pyodide.asm.js  | sort -u\n  const fs = nodeFSMod;\n  const crypto = await import(\"node:crypto\");\n  const ws = await import(\"ws\");\n  const child_process = await import(\"node:child_process\");\n  const node_modules: { [mode: string]: any } = {\n    fs,\n    crypto,\n    ws,\n    child_process,\n  };\n  // Since we're in an ES6 module, this is only modifying the module namespace,\n  // it's still private to Pyodide.\n  (globalThis as any).require = function (mod: string): any {\n    return node_modules[mod];\n  };\n}\n\nfunction node_resolvePath(path: string, base?: string): string {\n  return nodePath.resolve(base || \".\", path);\n}\n\nfunction browser_resolvePath(path: string, base?: string): string {\n  if (base === undefined) {\n    // @ts-ignore\n    base = location;\n  }\n  return new URL(path, base).toString();\n}\n\nexport let resolvePath: (rest: string, base?: string) => string;\nif (IN_NODE) {\n  resolvePath = node_resolvePath;\n} else {\n  resolvePath = browser_resolvePath;\n}\n\n/**\n * Get the path separator. If we are on Linux or in the browser, it's /.\n * In Windows, it's \\.\n * @private\n */\nexport let pathSep: string;\n\nif (!IN_NODE) {\n  pathSep = \"/\";\n}\n\n/**\n * Load a binary file, only for use in Node. If the path explicitly is a URL,\n * then fetch from a URL, else load from the file system.\n * @param indexURL base path to resolve relative paths\n * @param path the path to load\n * @param checksum sha-256 checksum of the package\n * @returns An ArrayBuffer containing the binary data\n * @private\n */\nfunction node_getBinaryResponse(\n  path: string,\n  _file_sub_resource_hash?: string | undefined, // Ignoring sub resource hash. See issue-2431.\n):\n  | { response: Promise<Response>; binary?: undefined }\n  | { binary: Promise<Uint8Array> } {\n  if (path.startsWith(\"file://\")) {\n    // handle file:// with filesystem operations rather than with fetch.\n    path = path.slice(\"file://\".length);\n  }\n  if (path.includes(\"://\")) {\n    // If it has a protocol, make a fetch request\n    return { response: fetch(path) };\n  } else {\n    // Otherwise get it from the file system\n    return {\n      binary: nodeFsPromisesMod\n        .readFile(path)\n        .then(\n          (data: Buffer) =>\n            new Uint8Array(data.buffer, data.byteOffset, data.byteLength),\n        ),\n    };\n  }\n}\n\n/**\n * Load a binary file, only for use in browser. Resolves relative paths against\n * indexURL.\n *\n * @param path the path to load\n * @param subResourceHash the sub resource hash for fetch() integrity check\n * @returns A Uint8Array containing the binary data\n * @private\n */\nfunction browser_getBinaryResponse(\n  path: string,\n  subResourceHash: string | undefined,\n): { response: Promise<Response>; binary?: undefined } {\n  const url = new URL(path, location as unknown as URL);\n  let options = subResourceHash ? { integrity: subResourceHash } : {};\n  return { response: fetch(url, options) };\n}\n\n/** @private */\nexport let getBinaryResponse: (\n  path: string,\n  file_sub_resource_hash?: string | undefined,\n) =>\n  | { response: Promise<Response>; binary?: undefined }\n  | { response?: undefined; binary: Promise<Uint8Array> };\nif (IN_NODE) {\n  getBinaryResponse = node_getBinaryResponse;\n} else {\n  getBinaryResponse = browser_getBinaryResponse;\n}\n\nexport async function loadBinaryFile(\n  path: string,\n  file_sub_resource_hash?: string | undefined,\n): Promise<Uint8Array> {\n  const { response, binary } = getBinaryResponse(path, file_sub_resource_hash);\n  if (binary) {\n    return binary;\n  }\n  const r = await response;\n  if (!r.ok) {\n    throw new Error(`Failed to load '${path}': request failed.`);\n  }\n  return new Uint8Array(await r.arrayBuffer());\n}\n\n/**\n * Currently loadScript is only used once to load `pyodide.asm.js`.\n * @param url\n * @private\n */\nexport let loadScript: (url: string) => Promise<void>;\n\nif (IN_BROWSER_MAIN_THREAD) {\n  // browser\n  loadScript = async (url) => await import(/* webpackIgnore: true */ url);\n} else if (IN_BROWSER_WEB_WORKER) {\n  // webworker\n  loadScript = async (url) => {\n    try {\n      // use importScripts in classic web worker\n      globalThis.importScripts(url);\n    } catch (e) {\n      // importScripts throws TypeError in a module type web worker, use import instead\n      if (e instanceof TypeError) {\n        await import(/* webpackIgnore: true */ url);\n      } else {\n        throw e;\n      }\n    }\n  };\n} else if (IN_NODE) {\n  loadScript = nodeLoadScript;\n} else {\n  throw new Error(\"Cannot determine runtime environment\");\n}\n\n/**\n * Load a text file and executes it as Javascript\n * @param url The path to load. May be a url or a relative file system path.\n * @private\n */\nasync function nodeLoadScript(url: string) {\n  if (url.startsWith(\"file://\")) {\n    // handle file:// with filesystem operations rather than with fetch.\n    url = url.slice(\"file://\".length);\n  }\n  if (url.includes(\"://\")) {\n    // If it's a url, load it with fetch then eval it.\n    nodeVmMod.runInThisContext(await (await fetch(url)).text());\n  } else {\n    // Otherwise, hopefully it is a relative path we can load from the file\n    // system.\n    await import(/* webpackIgnore: true */ nodeUrlMod.pathToFileURL(url).href);\n  }\n}\n\nexport async function loadLockFile(lockFileURL: string): Promise<Lockfile> {\n  if (IN_NODE) {\n    await initNodeModules();\n    const package_string = await nodeFsPromisesMod.readFile(lockFileURL, {\n      encoding: \"utf8\",\n    });\n    return JSON.parse(package_string);\n  } else {\n    let response = await fetch(lockFileURL);\n    return await response.json();\n  }\n}\n\n/**\n * Calculate the directory name of the current module.\n * This is used to guess the indexURL when it is not provided.\n */\nexport async function calculateDirname(): Promise<string> {\n  if (IN_NODE_COMMONJS) {\n    return __dirname;\n  }\n\n  let err: Error;\n  try {\n    throw new Error();\n  } catch (e) {\n    err = e as Error;\n  }\n  let fileName = ErrorStackParser.parse(err)[0].fileName!;\n\n  if (IN_NODE && !fileName.startsWith(\"file://\")) {\n    fileName = `file://${fileName}`; // Error stack filenames are not starting with `file://` in `Bun`\n  }\n\n  if (IN_NODE_ESM) {\n    const nodePath = await import(\"node:path\");\n    const nodeUrl = await import(\"node:url\");\n\n    // FIXME: We would like to use import.meta.url here,\n    // but mocha seems to mess with compiling typescript files to ES6.\n    return nodeUrl.fileURLToPath(nodePath.dirname(fileName));\n  }\n\n  const indexOfLastSlash = fileName.lastIndexOf(pathSep);\n  if (indexOfLastSlash === -1) {\n    throw new Error(\n      \"Could not extract indexURL path from pyodide module location\",\n    );\n  }\n  return fileName.slice(0, indexOfLastSlash);\n}\n\n/**\n * Ensure that the directory exists before trying to download files into it (Node.js only).\n * @param dir The directory to ensure exists\n */\nexport async function ensureDirNode(dir: string) {\n  if (!IN_NODE) {\n    return;\n  }\n\n  try {\n    // Check if the `installBaseUrl` directory exists\n    await nodeFsPromisesMod.stat(dir); // Use `.stat()` which works even on ASAR archives of Electron apps, while `.access` doesn't.\n  } catch {\n    // If it doesn't exist, make it. Call mkdir() here only when necessary after checking the existence to avoid an error on read-only file systems. See https://github.com/pyodide/pyodide/issues/4736\n    await nodeFsPromisesMod.mkdir(dir, {\n      recursive: true,\n    });\n  }\n}\n", "import { Modu<PERSON> } from \"./types\";\n\n/**\n * @private\n */\nasync function syncfs(m: Module, direction: boolean): Promise<void> {\n  return new Promise((resolve, reject) => {\n    m.FS.syncfs(direction, (err: any) => {\n      if (err) {\n        reject(err);\n      } else {\n        resolve();\n      }\n    });\n  });\n}\n\n/**\n * @private\n */\nexport async function syncLocalToRemote(m: Module): Promise<void> {\n  return await syncfs(m, false);\n}\n\n/**\n * @private\n */\nexport async function syncRemoteToLocal(m: Module): Promise<void> {\n  return await syncfs(m, true);\n}\n\n/**\n * @private\n */\nexport function initializeNativeFS(module: Module) {\n  const FS = module.FS;\n  const MEMFS = module.FS.filesystems.MEMFS;\n  const PATH = module.PATH;\n\n  const nativeFSAsync = {\n    // DIR_MODE: {{{ cDefine('S_IFDIR') }}} | 511 /* 0777 */,\n    // FILE_MODE: {{{ cDefine('S_IFREG') }}} | 511 /* 0777 */,\n    DIR_MODE: 16384 | 511,\n    FILE_MODE: 32768 | 511,\n    mount: function (mount: any) {\n      if (!mount.opts.fileSystemHandle) {\n        throw new Error(\"opts.fileSystemHandle is required\");\n      }\n\n      // reuse all of the core MEMFS functionality\n      return MEMFS.mount.apply(null, arguments);\n    },\n    syncfs: async (mount: any, populate: Boolean, callback: Function) => {\n      try {\n        const local = nativeFSAsync.getLocalSet(mount);\n        const remote = await nativeFSAsync.getRemoteSet(mount);\n        const src = populate ? remote : local;\n        const dst = populate ? local : remote;\n        await nativeFSAsync.reconcile(mount, src, dst);\n        callback(null);\n      } catch (e) {\n        callback(e);\n      }\n    },\n    // Returns file set of emscripten's filesystem at the mountpoint.\n    getLocalSet: (mount: any) => {\n      let entries = Object.create(null);\n\n      function isRealDir(p: string) {\n        return p !== \".\" && p !== \"..\";\n      }\n\n      function toAbsolute(root: string) {\n        return (p: string) => {\n          return PATH.join2(root, p);\n        };\n      }\n\n      let check = FS.readdir(mount.mountpoint)\n        .filter(isRealDir)\n        .map(toAbsolute(mount.mountpoint));\n\n      while (check.length) {\n        let path = check.pop();\n        let stat = FS.stat(path);\n\n        if (FS.isDir(stat.mode)) {\n          check.push.apply(\n            check,\n            FS.readdir(path).filter(isRealDir).map(toAbsolute(path)),\n          );\n        }\n\n        entries[path] = { timestamp: stat.mtime, mode: stat.mode };\n      }\n\n      return { type: \"local\", entries: entries };\n    },\n    // Returns file set of the real, on-disk filesystem at the mountpoint.\n    getRemoteSet: async (mount: any) => {\n      // TODO: this should be a map.\n      const entries = Object.create(null);\n\n      const handles = await getFsHandles(mount.opts.fileSystemHandle);\n      for (const [path, handle] of handles) {\n        if (path === \".\") continue;\n\n        entries[PATH.join2(mount.mountpoint, path)] = {\n          timestamp:\n            handle.kind === \"file\"\n              ? (await handle.getFile()).lastModifiedDate\n              : new Date(),\n          mode:\n            handle.kind === \"file\"\n              ? nativeFSAsync.FILE_MODE\n              : nativeFSAsync.DIR_MODE,\n        };\n      }\n\n      return { type: \"remote\", entries, handles };\n    },\n    loadLocalEntry: (path: string) => {\n      const lookup = FS.lookupPath(path);\n      const node = lookup.node;\n      const stat = FS.stat(path);\n\n      if (FS.isDir(stat.mode)) {\n        return { timestamp: stat.mtime, mode: stat.mode };\n      } else if (FS.isFile(stat.mode)) {\n        node.contents = MEMFS.getFileDataAsTypedArray(node);\n        return {\n          timestamp: stat.mtime,\n          mode: stat.mode,\n          contents: node.contents,\n        };\n      } else {\n        throw new Error(\"node type not supported\");\n      }\n    },\n    storeLocalEntry: (path: string, entry: any) => {\n      if (FS.isDir(entry[\"mode\"])) {\n        FS.mkdirTree(path, entry[\"mode\"]);\n      } else if (FS.isFile(entry[\"mode\"])) {\n        FS.writeFile(path, entry[\"contents\"], { canOwn: true });\n      } else {\n        throw new Error(\"node type not supported\");\n      }\n\n      FS.chmod(path, entry[\"mode\"]);\n      FS.utime(path, entry[\"timestamp\"], entry[\"timestamp\"]);\n    },\n    removeLocalEntry: (path: string) => {\n      var stat = FS.stat(path);\n\n      if (FS.isDir(stat.mode)) {\n        FS.rmdir(path);\n      } else if (FS.isFile(stat.mode)) {\n        FS.unlink(path);\n      }\n    },\n    loadRemoteEntry: async (handle: any) => {\n      if (handle.kind === \"file\") {\n        const file = await handle.getFile();\n        return {\n          contents: new Uint8Array(await file.arrayBuffer()),\n          mode: nativeFSAsync.FILE_MODE,\n          timestamp: file.lastModifiedDate,\n        };\n      } else if (handle.kind === \"directory\") {\n        return {\n          mode: nativeFSAsync.DIR_MODE,\n          timestamp: new Date(),\n        };\n      } else {\n        throw new Error(\"unknown kind: \" + handle.kind);\n      }\n    },\n    storeRemoteEntry: async (handles: any, path: string, entry: any) => {\n      const parentDirHandle = handles.get(PATH.dirname(path));\n      const handle = FS.isFile(entry.mode)\n        ? await parentDirHandle.getFileHandle(PATH.basename(path), {\n            create: true,\n          })\n        : await parentDirHandle.getDirectoryHandle(PATH.basename(path), {\n            create: true,\n          });\n      if (handle.kind === \"file\") {\n        const writable = await handle.createWritable();\n        await writable.write(entry.contents);\n        await writable.close();\n      }\n      handles.set(path, handle);\n    },\n    removeRemoteEntry: async (handles: any, path: string) => {\n      const parentDirHandle = handles.get(PATH.dirname(path));\n      await parentDirHandle.removeEntry(PATH.basename(path));\n      handles.delete(path);\n    },\n    reconcile: async (mount: any, src: any, dst: any) => {\n      let total = 0;\n\n      const create: Array<string> = [];\n      Object.keys(src.entries).forEach(function (key) {\n        const e = src.entries[key];\n        const e2 = dst.entries[key];\n        if (\n          !e2 ||\n          (FS.isFile(e.mode) &&\n            e[\"timestamp\"].getTime() > e2[\"timestamp\"].getTime())\n        ) {\n          create.push(key);\n          total++;\n        }\n      });\n      // sort paths in ascending order so directory entries are created\n      // before the files inside them\n      create.sort();\n\n      const remove: Array<string> = [];\n      Object.keys(dst.entries).forEach(function (key) {\n        if (!src.entries[key]) {\n          remove.push(key);\n          total++;\n        }\n      });\n      // sort paths in descending order so files are deleted before their\n      // parent directories\n      remove.sort().reverse();\n\n      if (!total) {\n        return;\n      }\n\n      const handles = src.type === \"remote\" ? src.handles : dst.handles;\n\n      for (const path of create) {\n        const relPath = PATH.normalize(\n          path.replace(mount.mountpoint, \"/\"),\n        ).substring(1);\n        if (dst.type === \"local\") {\n          const handle = handles.get(relPath);\n          const entry = await nativeFSAsync.loadRemoteEntry(handle);\n          nativeFSAsync.storeLocalEntry(path, entry);\n        } else {\n          const entry = nativeFSAsync.loadLocalEntry(path);\n          await nativeFSAsync.storeRemoteEntry(handles, relPath, entry);\n        }\n      }\n\n      for (const path of remove) {\n        if (dst.type === \"local\") {\n          nativeFSAsync.removeLocalEntry(path);\n        } else {\n          const relPath = PATH.normalize(\n            path.replace(mount.mountpoint, \"/\"),\n          ).substring(1);\n          await nativeFSAsync.removeRemoteEntry(handles, relPath);\n        }\n      }\n    },\n  };\n\n  module.FS.filesystems.NATIVEFS_ASYNC = nativeFSAsync;\n}\n\nconst getFsHandles = async (dirHandle: any) => {\n  const handles: any = [];\n\n  async function collect(curDirHandle: any) {\n    for await (const entry of curDirHandle.values()) {\n      handles.push(entry);\n      if (entry.kind === \"directory\") {\n        await collect(entry);\n      }\n    }\n  }\n\n  await collect(dirHandle);\n\n  const result = new Map();\n  result.set(\".\", dirHandle);\n  for (const handle of handles) {\n    const relativePath = (await dirHandle.resolve(handle)).join(\"/\");\n    result.set(relativePath, handle);\n  }\n  return result;\n};\n", "/** @private */\n\nimport { ConfigType } from \"./pyodide\";\nimport { initializeNativeFS } from \"./nativefs\";\nimport { loadBinaryFile, getBinaryResponse } from \"./compat\";\nimport { API, PreRunFunc } from \"./types\";\n\n/**\n * @private\n * @hidden\n */\nexport interface EmscriptenSettings {\n  readonly noImageDecoding?: boolean;\n  readonly noAudioDecoding?: boolean;\n  readonly noWasmDecoding?: boolean;\n  readonly preRun: readonly PreRunFunc[];\n  readonly quit: (status: number, toThrow: Error) => void;\n  readonly print?: (a: string) => void;\n  readonly printErr?: (a: string) => void;\n  readonly arguments: readonly string[];\n  readonly instantiateWasm?: (\n    imports: { [key: string]: any },\n    successCallback: (\n      instance: WebAssembly.Instance,\n      module: WebAssembly.Module,\n    ) => void,\n  ) => void;\n  readonly API: API;\n  readonly locateFile: (file: string) => string;\n\n  exited?: { readonly status: number; readonly toThrow: Error };\n  noInitialRun?: boolean;\n  INITIAL_MEMORY?: number;\n}\n\n/**\n * Get the base settings to use to load Pyodide.\n *\n * @private\n */\nexport function createSettings(config: ConfigType): EmscriptenSettings {\n  const settings: EmscriptenSettings = {\n    noImageDecoding: true,\n    noAudioDecoding: true,\n    noWasmDecoding: false,\n    preRun: getFileSystemInitializationFuncs(config),\n    quit(status: number, toThrow: Error) {\n      // It's a little bit hacky that we set this on the settings object but\n      // it's not that easy to get access to the Module object from here.\n      settings.exited = { status, toThrow };\n      throw toThrow;\n    },\n    print: config.stdout,\n    printErr: config.stderr,\n    arguments: config.args,\n    API: { config } as API,\n    // Emscripten calls locateFile exactly one time with argument\n    // pyodide.asm.wasm to get the URL it should download it from.\n    //\n    // If we set instantiateWasm the return value of locateFile actually is\n    // unused, but Emscripten calls it anyways. We set instantiateWasm except\n    // when compiling with source maps, see comment in getInstantiateWasmFunc().\n    //\n    // It also is called when Emscripten tries to find a dependency of a shared\n    // library but it failed to find it in the file system. But for us that\n    // means dependency resolution has already failed and we want to throw an\n    // error anyways.\n    locateFile: (path: string) => config.indexURL + path,\n    instantiateWasm: getInstantiateWasmFunc(config.indexURL),\n  };\n  return settings;\n}\n\n/**\n * Make the home directory inside the virtual file system,\n * then change the working directory to it.\n *\n * @param Module The Emscripten Module.\n * @param path The path to the home directory.\n * @private\n */\nfunction createHomeDirectory(path: string): PreRunFunc {\n  return function (Module) {\n    const fallbackPath = \"/\";\n    try {\n      Module.FS.mkdirTree(path);\n    } catch (e) {\n      console.error(`Error occurred while making a home directory '${path}':`);\n      console.error(e);\n      console.error(`Using '${fallbackPath}' for a home directory instead`);\n      path = fallbackPath;\n    }\n    Module.FS.chdir(path);\n  };\n}\n\nfunction setEnvironment(env: { [key: string]: string }): PreRunFunc {\n  return function (Module) {\n    Object.assign(Module.ENV, env);\n  };\n}\n\n/**\n * Mount local directories to the virtual file system. Only for Node.js.\n * @param mounts The list of paths to mount.\n */\nfunction mountLocalDirectories(mounts: string[]): PreRunFunc {\n  return (Module) => {\n    for (const mount of mounts) {\n      Module.FS.mkdirTree(mount);\n      Module.FS.mount(Module.FS.filesystems.NODEFS, { root: mount }, mount);\n    }\n  };\n}\n\n/**\n * Install the Python standard library to the virtual file system.\n *\n * Previously, this was handled by Emscripten's file packager (pyodide.asm.data).\n * However, using the file packager means that we have only one version\n * of the standard library available. We want to be able to use different\n * versions of the standard library, for example:\n *\n * - Use compiled(.pyc) or uncompiled(.py) standard library.\n * - Remove unused modules or add additional modules using bundlers like pyodide-pack.\n *\n * @param stdlibURL The URL for the Python standard library\n */\nfunction installStdlib(stdlibURL: string): PreRunFunc {\n  const stdlibPromise: Promise<Uint8Array> = loadBinaryFile(stdlibURL);\n  return (Module) => {\n    /* @ts-ignore */\n    const pymajor = Module._py_version_major();\n    /* @ts-ignore */\n    const pyminor = Module._py_version_minor();\n\n    Module.FS.mkdirTree(\"/lib\");\n    Module.FS.mkdirTree(`/lib/python${pymajor}.${pyminor}/site-packages`);\n\n    Module.addRunDependency(\"install-stdlib\");\n\n    stdlibPromise\n      .then((stdlib: Uint8Array) => {\n        Module.FS.writeFile(`/lib/python${pymajor}${pyminor}.zip`, stdlib);\n      })\n      .catch((e) => {\n        console.error(\"Error occurred while installing the standard library:\");\n        console.error(e);\n      })\n      .finally(() => {\n        Module.removeRunDependency(\"install-stdlib\");\n      });\n  };\n}\n\n/**\n * Initialize the virtual file system, before loading Python interpreter.\n * @private\n */\nfunction getFileSystemInitializationFuncs(config: ConfigType): PreRunFunc[] {\n  let stdLibURL;\n  if (config.stdLibURL != undefined) {\n    stdLibURL = config.stdLibURL;\n  } else {\n    stdLibURL = config.indexURL + \"python_stdlib.zip\";\n  }\n\n  return [\n    installStdlib(stdLibURL),\n    createHomeDirectory(config.env.HOME),\n    setEnvironment(config.env),\n    mountLocalDirectories(config._node_mounts),\n    initializeNativeFS,\n  ];\n}\n\nfunction getInstantiateWasmFunc(\n  indexURL: string,\n): EmscriptenSettings[\"instantiateWasm\"] {\n  // @ts-ignore\n  if (SOURCEMAP || typeof WasmOffsetConverter !== \"undefined\") {\n    // According to the docs:\n    //\n    // \"Sanitizers or source map is currently not supported if overriding\n    // WebAssembly instantiation with Module.instantiateWasm.\"\n    // https://emscripten.org/docs/api_reference/module.html?highlight=instantiatewasm#Module.instantiateWasm\n    //\n    // typeof WasmOffsetConverter !== \"undefined\" checks for asan.\n    return;\n  }\n  const { binary, response } = getBinaryResponse(indexURL + \"pyodide.asm.wasm\");\n  return function (\n    imports: { [key: string]: any },\n    successCallback: (\n      instance: WebAssembly.Instance,\n      module: WebAssembly.Module,\n    ) => void,\n  ) {\n    (async function () {\n      try {\n        let res: WebAssembly.WebAssemblyInstantiatedSource;\n        if (response) {\n          res = await WebAssembly.instantiateStreaming(response, imports);\n        } else {\n          res = await WebAssembly.instantiate(await binary, imports);\n        }\n        const { instance, module } = res;\n        successCallback(instance, module);\n      } catch (e) {\n        console.warn(\"wasm instantiation failed!\");\n        console.warn(e);\n      }\n    })();\n\n    return {}; // Compiling asynchronously, no exports.\n  };\n}\n", "/**\n *\n * The Pyodide version.\n *\n * The version here is a Python version, following :pep:`440`. This is different\n * from the version in ``package.json`` which follows the node package manager\n * version convention.\n */\nexport const version: string = \"0.27.3\";\n", "/**\n * The main bootstrap code for loading pyodide.\n */\nimport {\n  calculateDirname,\n  loadScript,\n  initNodeModules,\n  resolvePath,\n  loadLockFile,\n} from \"./compat\";\n\nimport { createSettings } from \"./emscripten-settings\";\nimport { version } from \"./version\";\n\nimport type { PyodideInterface } from \"./api.js\";\nimport type { TypedArray, Module, PackageData } from \"./types\";\nimport type { EmscriptenSettings } from \"./emscripten-settings\";\nimport type { SnapshotConfig } from \"./snapshot\";\nexport type { PyodideInterface, TypedArray };\n\nexport { version, type PackageData };\n\ndeclare function _createPyodideModule(\n  settings: EmscriptenSettings,\n): Promise<Module>;\n\n// BUILD_ID is generated from hashing together pyodide.asm.js and\n// pyodide.asm.wasm in esbuild.config.outer.mjs\n//\n// It is used to check that memory snapshots were generated by the same build of\n// the runtime that is trying to use them. Attempting to use a snapshot from a\n// different build will fail badly. See logic in snapshot.ts.\ndeclare const BUILD_ID: string;\n\n/**\n * See documentation for loadPyodide.\n * @hidden\n */\nexport type ConfigType = {\n  indexURL: string;\n  packageCacheDir: string;\n  lockFileURL: string;\n  fullStdLib?: boolean;\n  stdLibURL?: string;\n  stdin?: () => string;\n  stdout?: (msg: string) => void;\n  stderr?: (msg: string) => void;\n  jsglobals?: object;\n  args: string[];\n  _node_mounts: string[];\n  env: { [key: string]: string };\n  packages: string[];\n  _makeSnapshot: boolean;\n  enableRunUntilComplete: boolean;\n  checkAPIVersion: boolean;\n  BUILD_ID: string;\n};\n\n/**\n * Load the main Pyodide wasm module and initialize it.\n *\n * @returns The :ref:`js-api-pyodide` module.\n * @example\n * async function main() {\n *   const pyodide = await loadPyodide({\n *     fullStdLib: true,\n *     stdout: (msg) => console.log(`Pyodide: ${msg}`),\n *   });\n *   console.log(\"Loaded Pyodide\");\n * }\n * main();\n */\nexport async function loadPyodide(\n  options: {\n    /**\n     * The URL from which Pyodide will load the main Pyodide runtime and\n     * packages. It is recommended that you leave this unchanged, providing an\n     * incorrect value can cause broken behavior.\n     *\n     * Default: The url that Pyodide is loaded from with the file name\n     * (``pyodide.js`` or ``pyodide.mjs``) removed.\n     */\n    indexURL?: string;\n\n    /**\n     * The file path where packages will be cached in node. If a package\n     * exists in ``packageCacheDir`` it is loaded from there, otherwise it is\n     * downloaded from the JsDelivr CDN and then cached into ``packageCacheDir``.\n     * Only applies when running in node; ignored in browsers.\n     *\n     * Default: same as indexURL\n     */\n    packageCacheDir?: string;\n\n    /**\n     * The URL from which Pyodide will load the Pyodide ``pyodide-lock.json`` lock\n     * file. You can produce custom lock files with :py:func:`micropip.freeze`.\n     * Default: ```${indexURL}/pyodide-lock.json```\n     */\n    lockFileURL?: string;\n    /**\n     * Load the full Python standard library. Setting this to false excludes\n     * unvendored modules from the standard library.\n     * Default: ``false``\n     */\n    fullStdLib?: boolean;\n    /**\n     * The URL from which to load the standard library ``python_stdlib.zip``\n     * file. This URL includes the most of the Python standard library. Some\n     * stdlib modules were unvendored, and can be loaded separately\n     * with ``fullStdLib: true`` option or by their package name.\n     * Default: ```${indexURL}/python_stdlib.zip```\n     */\n    stdLibURL?: string;\n    /**\n     * Override the standard input callback. Should ask the user for one line of\n     * input. The :js:func:`pyodide.setStdin` function is more flexible and\n     * should be preferred.\n     */\n    stdin?: () => string;\n    /**\n     * Override the standard output callback. The :js:func:`pyodide.setStdout`\n     * function is more flexible and should be preferred in most cases, but\n     * depending on the ``args`` passed to ``loadPyodide``, Pyodide may write to\n     * stdout on startup, which can only be controlled by passing a custom\n     * ``stdout`` function.\n     */\n    stdout?: (msg: string) => void;\n    /**\n     * Override the standard error output callback. The\n     * :js:func:`pyodide.setStderr` function is more flexible and should be\n     * preferred in most cases, but depending on the ``args`` passed to\n     * ``loadPyodide``, Pyodide may write to stdout on startup, which can only\n     * be controlled by passing a custom ``stdout`` function.\n     */\n    stderr?: (msg: string) => void;\n    /**\n     * The object that Pyodide will use for the ``js`` module.\n     * Default: ``globalThis``\n     */\n    jsglobals?: object;\n    /**\n     * Command line arguments to pass to Python on startup. See `Python command\n     * line interface options\n     * <https://docs.python.org/3.10/using/cmdline.html#interface-options>`_ for\n     * more details. Default: ``[]``\n     */\n    args?: string[];\n    /**\n     * Environment variables to pass to Python. This can be accessed inside of\n     * Python at runtime via :py:data:`os.environ`. Certain environment variables change\n     * the way that Python loads:\n     * https://docs.python.org/3.10/using/cmdline.html#environment-variables\n     * Default: ``{}``.\n     * If ``env.HOME`` is undefined, it will be set to a default value of\n     * ``\"/home/<USER>\"``\n     */\n    env?: { [key: string]: string };\n    /**\n     * A list of packages to load as Pyodide is initializing.\n     *\n     * This is the same as loading the packages with\n     * :js:func:`pyodide.loadPackage` after Pyodide is loaded except using the\n     * ``packages`` option is more efficient because the packages are downloaded\n     * while Pyodide bootstraps itself.\n     */\n    packages?: string[];\n    /**\n     * Opt into the old behavior where :js:func:`PyProxy.toString() <pyodide.ffi.PyProxy.toString>`\n     * calls :py:func:`repr` and not :py:class:`str() <str>`.\n     * @deprecated\n     */\n    pyproxyToStringRepr?: boolean;\n    /**\n     * Make loop.run_until_complete() function correctly using stack switching\n     */\n    enableRunUntilComplete?: boolean;\n    /**\n     * If true (default), throw an error if the version of Pyodide core does not\n     * match the version of the Pyodide js package.\n     */\n    checkAPIVersion?: boolean;\n    /**\n     * Used by the cli runner. If we want to detect a virtual environment from\n     * the host file system, it needs to be visible from when `main()` is\n     * called. The directories in this list will be mounted at the same address\n     * into the Emscripten file system so that virtual environments work in the\n     * cli runner.\n     * @ignore\n     */\n    _node_mounts?: string[];\n    /** @ignore */\n    _makeSnapshot?: boolean;\n    /** @ignore */\n    _loadSnapshot?:\n      | Uint8Array\n      | ArrayBuffer\n      | PromiseLike<Uint8Array | ArrayBuffer>;\n    /** @ignore */\n    _snapshotDeserializer?: (obj: any) => any;\n  } = {},\n): Promise<PyodideInterface> {\n  await initNodeModules();\n  let indexURL = options.indexURL || (await calculateDirname());\n  indexURL = resolvePath(indexURL); // A relative indexURL causes havoc.\n  if (!indexURL.endsWith(\"/\")) {\n    indexURL += \"/\";\n  }\n  options.indexURL = indexURL;\n\n  const default_config = {\n    fullStdLib: false,\n    jsglobals: globalThis,\n    stdin: globalThis.prompt ? globalThis.prompt : undefined,\n    lockFileURL: indexURL + \"pyodide-lock.json\",\n    args: [],\n    _node_mounts: [],\n    env: {},\n    packageCacheDir: indexURL,\n    packages: [],\n    enableRunUntilComplete: false,\n    checkAPIVersion: true,\n    BUILD_ID,\n  };\n  const config = Object.assign(default_config, options) as ConfigType;\n  config.env.HOME ??= \"/home/<USER>\";\n  /**\n   * `PyErr_Print()` will call `exit()` if the exception is a `SystemError`.\n   * This shuts down the Python interpreter, which is a change in behavior from\n   * what happened before. In order to avoid this, we set the `inspect` config\n   * parameter which prevents `PyErr_Print()` from calling `exit()`. Except in\n   * the cli runner, we actually do want to exit. So set default to true and in\n   * cli runner we explicitly set it to false.\n   */\n  config.env.PYTHONINSPECT ??= \"1\";\n  const emscriptenSettings = createSettings(config);\n  const API = emscriptenSettings.API;\n  API.lockFilePromise = loadLockFile(config.lockFileURL);\n\n  // If the pyodide.asm.js script has been imported, we can skip the dynamic import\n  // Users can then do a static import of the script in environments where\n  // dynamic importing is not allowed or not desirable, like module-type service workers\n  if (typeof _createPyodideModule !== \"function\") {\n    const scriptSrc = `${config.indexURL}pyodide.asm.js`;\n    await loadScript(scriptSrc);\n  }\n\n  let snapshot: Uint8Array | undefined = undefined;\n  if (options._loadSnapshot) {\n    const snp = await options._loadSnapshot;\n    if (ArrayBuffer.isView(snp)) {\n      snapshot = snp;\n    } else {\n      snapshot = new Uint8Array(snp);\n    }\n    emscriptenSettings.noInitialRun = true;\n    // @ts-ignore\n    emscriptenSettings.INITIAL_MEMORY = snapshot.length;\n  }\n\n  // _createPyodideModule is specified in the Makefile by the linker flag:\n  // `-s EXPORT_NAME=\"'_createPyodideModule'\"`\n  const Module = await _createPyodideModule(emscriptenSettings);\n  // Handle early exit\n  if (emscriptenSettings.exited) {\n    throw emscriptenSettings.exited.toThrow;\n  }\n  if (options.pyproxyToStringRepr) {\n    API.setPyProxyToStringMethod(true);\n  }\n\n  if (API.version !== version && config.checkAPIVersion) {\n    throw new Error(`\\\nPyodide version does not match: '${version}' <==> '${API.version}'. \\\nIf you updated the Pyodide version, make sure you also updated the 'indexURL' parameter passed to loadPyodide.\\\n`);\n  }\n  // Disable further loading of Emscripten file_packager stuff.\n  Module.locateFile = (path: string) => {\n    throw new Error(\"Didn't expect to load any more file_packager files!\");\n  };\n\n  let snapshotConfig: SnapshotConfig | undefined = undefined;\n  if (snapshot) {\n    snapshotConfig = API.restoreSnapshot(snapshot);\n  }\n  // runPython works starting after the call to finalizeBootstrap.\n  const pyodide = API.finalizeBootstrap(\n    snapshotConfig,\n    options._snapshotDeserializer,\n  );\n  API.sys.path.insert(0, API.config.env.HOME);\n\n  if (!pyodide.version.includes(\"dev\")) {\n    // Currently only used in Node to download packages the first time they are\n    // loaded. But in other cases it's harmless.\n    API.setCdnUrl(`https://cdn.jsdelivr.net/pyodide/v${pyodide.version}/full/`);\n  }\n  API._pyodide.set_excepthook();\n  await API.packageIndexReady;\n  // I think we want this initializeStreams call to happen after\n  // packageIndexReady? I don't remember why.\n  API.initializeStreams(config.stdin, config.stdout, config.stderr);\n  return pyodide;\n}\n"], "mappings": "w4BAAA,IAAAA,GAAA,GAAAC,GAAAD,GAAA,iBAAAE,EAAA,YAAAC,ICIA,SAASC,GAAUC,EAAG,CACpB,MAAO,CAAC,MAAM,WAAWA,CAAC,CAAC,GAAK,SAASA,CAAC,CAC5C,CAFSC,EAAAF,GAAA,aAIT,SAASG,EAAYC,EAAK,CACxB,OAAOA,EAAI,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAI,UAAU,CAAC,CACtD,CAFSF,EAAAC,EAAA,eAIT,SAASE,EAAQC,EAAG,CAClB,OAAO,UAAY,CACjB,OAAO,KAAKA,CAAC,CACf,CACF,CAJSJ,EAAAG,EAAA,WAMT,IAAIE,EAAe,CAAC,gBAAiB,SAAU,WAAY,YAAY,EACnEC,EAAe,CAAC,eAAgB,YAAY,EAC5CC,EAAc,CAAC,WAAY,eAAgB,QAAQ,EACnDC,GAAa,CAAC,MAAM,EACpBC,GAAc,CAAC,YAAY,EAE3BC,EAAQL,EAAa,OACvBC,EACAC,EACAC,GACAC,EACF,EAoEA,SAASE,EAAWC,EAAK,CACvB,GAAKA,EACL,QAASC,EAAI,EAAGA,EAAIH,EAAM,OAAQG,IAC5BD,EAAIF,EAAMG,CAAC,CAAC,IAAM,QACpB,KAAK,MAAQZ,EAAYS,EAAMG,CAAC,CAAC,CAAC,EAAED,EAAIF,EAAMG,CAAC,CAAC,CAAC,CAGvD,CAPSb,EAAAW,EAAA,cASTA,EAAW,UAAY,CACrB,QAAS,UAAY,CACnB,OAAO,KAAK,IACd,EACA,QAAS,SAAUG,EAAG,CACpB,GAAI,OAAO,UAAU,SAAS,KAAKA,CAAC,IAAM,iBACxC,MAAM,IAAI,UAAU,uBAAuB,EAE7C,KAAK,KAAOA,CACd,EAEA,cAAe,UAAY,CACzB,OAAO,KAAK,UACd,EACA,cAAe,SAAUA,EAAG,CAC1B,GAAIA,aAAaH,EACf,KAAK,WAAaG,UACTA,aAAa,OACtB,KAAK,WAAa,IAAIH,EAAWG,CAAC,MAElC,OAAM,IAAI,UAAU,6CAA6C,CAErE,EAEA,SAAU,UAAY,CACpB,IAAIC,EAAW,KAAK,YAAY,GAAK,GACjCC,EAAa,KAAK,cAAc,GAAK,GACrCC,EAAe,KAAK,gBAAgB,GAAK,GACzCC,EAAe,KAAK,gBAAgB,GAAK,GAC7C,OAAI,KAAK,UAAU,EACbH,EAEA,WAAaA,EAAW,IAAMC,EAAa,IAAMC,EAAe,IAG7D,UAAYD,EAAa,IAAMC,EAEpCC,EAEAA,EACA,KACAH,EACA,IACAC,EACA,IACAC,EACA,IAGGF,EAAW,IAAMC,EAAa,IAAMC,CAC7C,CACF,EAEAN,EAAW,WAAaX,EAAA,SAAgCE,EAAK,CAC3D,IAAIiB,EAAiBjB,EAAI,QAAQ,GAAG,EAChCkB,EAAelB,EAAI,YAAY,GAAG,EAElCgB,EAAehB,EAAI,UAAU,EAAGiB,CAAc,EAC9CE,EAAOnB,EAAI,UAAUiB,EAAiB,EAAGC,CAAY,EAAE,MAAM,GAAG,EAChEE,EAAiBpB,EAAI,UAAUkB,EAAe,CAAC,EAEnD,GAAIE,EAAe,QAAQ,GAAG,IAAM,EAClC,IAAIC,EAAQ,gCAAgC,KAAKD,EAAgB,EAAE,EAC/DP,EAAWQ,EAAM,CAAC,EAClBP,EAAaO,EAAM,CAAC,EACpBN,EAAeM,EAAM,CAAC,EAG5B,OAAO,IAAIZ,EAAW,CACpB,aAAcO,EACd,KAAMG,GAAQ,OACd,SAAUN,EACV,WAAYC,GAAc,OAC1B,aAAcC,GAAgB,MAChC,CAAC,CACH,EAtBwB,0BAwBxB,IAASJ,EAAI,EAAGA,EAAIR,EAAa,OAAQQ,IACvCF,EAAW,UAAU,MAAQV,EAAYI,EAAaQ,CAAC,CAAC,CAAC,EAAIV,EAC3DE,EAAaQ,CAAC,CAChB,EACAF,EAAW,UAAU,MAAQV,EAAYI,EAAaQ,CAAC,CAAC,CAAC,EAAK,SAAUT,EAAG,CACzE,OAAO,SAAUU,EAAG,CAClB,KAAKV,CAAC,EAAI,EAAQU,CACpB,CACF,EAAGT,EAAaQ,CAAC,CAAC,EARX,IAAAA,EAWT,IAASW,EAAI,EAAGA,EAAIlB,EAAa,OAAQkB,IACvCb,EAAW,UAAU,MAAQV,EAAYK,EAAakB,CAAC,CAAC,CAAC,EAAIrB,EAC3DG,EAAakB,CAAC,CAChB,EACAb,EAAW,UAAU,MAAQV,EAAYK,EAAakB,CAAC,CAAC,CAAC,EAAK,SAAUpB,EAAG,CACzE,OAAO,SAAUU,EAAG,CAClB,GAAI,CAAChB,GAAUgB,CAAC,EACd,MAAM,IAAI,UAAUV,EAAI,mBAAmB,EAE7C,KAAKA,CAAC,EAAI,OAAOU,CAAC,CACpB,CACF,EAAGR,EAAakB,CAAC,CAAC,EAXX,IAAAA,EAcT,IAASC,EAAI,EAAGA,EAAIlB,EAAY,OAAQkB,IACtCd,EAAW,UAAU,MAAQV,EAAYM,EAAYkB,CAAC,CAAC,CAAC,EAAItB,EAC1DI,EAAYkB,CAAC,CACf,EACAd,EAAW,UAAU,MAAQV,EAAYM,EAAYkB,CAAC,CAAC,CAAC,EAAK,SAAUrB,EAAG,CACxE,OAAO,SAAUU,EAAG,CAClB,KAAKV,CAAC,EAAI,OAAOU,CAAC,CACpB,CACF,EAAGP,EAAYkB,CAAC,CAAC,EARV,IAAAA,EAWFC,EAAQf,ECzMf,SAASgB,IAAmB,CAC1B,IAAIC,EAAyB,iCACzBC,EAA4B,8BAEhC,MAAO,CAOL,MAAOC,EAAA,SAAiCC,EAA4B,CAClE,GAAIA,EAAM,OAASA,EAAM,MAAM,MAAMH,CAAsB,EACzD,OAAO,KAAK,YAAYG,CAAK,EACxB,GAAIA,EAAM,MACf,OAAO,KAAK,gBAAgBA,CAAK,EAEjC,MAAM,IAAI,MAAM,iCAAiC,CAErD,EARO,2BAWP,gBAAiBD,EAAA,SAA2CE,EAAS,CAEnE,GAAIA,EAAQ,QAAQ,GAAG,IAAM,GAC3B,MAAO,CAACA,CAAO,EAGjB,IAAIC,EAAS,+BACTC,EAAQD,EAAO,KAAKD,EAAQ,QAAQ,QAAS,EAAE,CAAC,EACpD,MAAO,CAACE,EAAM,CAAC,EAAGA,EAAM,CAAC,GAAK,OAAWA,EAAM,CAAC,GAAK,MAAS,CAChE,EATiB,qCAWjB,YAAaJ,EAAA,SAAuCC,EAAO,CACzD,IAAII,EAAWJ,EAAM,MAAM,MAAM;AAAA,CAAI,EAAE,OAAO,SAAUK,EAAM,CAC5D,MAAO,CAAC,CAACA,EAAK,MAAMR,CAAsB,CAC5C,EAAG,IAAI,EAEP,OAAOO,EAAS,IAAI,SAAUC,EAAM,CAC9BA,EAAK,QAAQ,QAAQ,EAAI,KAE3BA,EAAOA,EACJ,QAAQ,aAAc,MAAM,EAC5B,QAAQ,6BAA8B,EAAE,GAE7C,IAAIC,EAAgBD,EACjB,QAAQ,OAAQ,EAAE,EAClB,QAAQ,eAAgB,GAAG,EAC3B,QAAQ,UAAW,EAAE,EAIpBE,EAAWD,EAAc,MAAM,YAAY,EAG/CA,EAAgBC,EACZD,EAAc,QAAQC,EAAS,CAAC,EAAG,EAAE,EACrCD,EAIJ,IAAIE,EAAgB,KAAK,gBACvBD,EAAWA,EAAS,CAAC,EAAID,CAC3B,EACIG,EAAgBF,GAAYD,GAAkB,OAC9CI,EACF,CAAC,OAAQ,aAAa,EAAE,QAAQF,EAAc,CAAC,CAAC,EAAI,GAChD,OACAA,EAAc,CAAC,EAErB,OAAO,IAAIG,EAAW,CACpB,aAAcF,EACd,SAAUC,EACV,WAAYF,EAAc,CAAC,EAC3B,aAAcA,EAAc,CAAC,EAC7B,OAAQH,CACV,CAAC,CACH,EAAG,IAAI,CACT,EA7Ca,iCA+Cb,gBAAiBN,EAAA,SAA2CC,EAAO,CACjE,IAAII,EAAWJ,EAAM,MAAM,MAAM;AAAA,CAAI,EAAE,OAAO,SAAUK,EAAM,CAC5D,MAAO,CAACA,EAAK,MAAMP,CAAyB,CAC9C,EAAG,IAAI,EAEP,OAAOM,EAAS,IAAI,SAAUC,EAAM,CASlC,GAPIA,EAAK,QAAQ,SAAS,EAAI,KAC5BA,EAAOA,EAAK,QACV,mDACA,KACF,GAGEA,EAAK,QAAQ,GAAG,IAAM,IAAMA,EAAK,QAAQ,GAAG,IAAM,GAEpD,OAAO,IAAIM,EAAW,CACpB,aAAcN,CAChB,CAAC,EAED,IAAIO,EAAoB,6BACpBC,EAAUR,EAAK,MAAMO,CAAiB,EACtCH,EAAeI,GAAWA,EAAQ,CAAC,EAAIA,EAAQ,CAAC,EAAI,OACpDL,EAAgB,KAAK,gBACvBH,EAAK,QAAQO,EAAmB,EAAE,CACpC,EAEA,OAAO,IAAID,EAAW,CACpB,aAAcF,EACd,SAAUD,EAAc,CAAC,EACzB,WAAYA,EAAc,CAAC,EAC3B,aAAcA,EAAc,CAAC,EAC7B,OAAQH,CACV,CAAC,CAEL,EAAG,IAAI,CACT,EApCiB,oCAqCnB,CACF,CAtHSN,EAAAH,GAAA,oBAwHT,IAAMkB,GAAmB,IAAIlB,GAG7B,IAAOmB,EAAQC,GC1IR,IAAMC,EACX,OAAO,SAAY,UACnB,OAAO,QAAQ,UAAa,UAC5B,OAAO,QAAQ,SAAS,MAAS,UACjC,CAAC,QAAQ,QAGEC,EACXD,GACA,OAAO,OAAW,KAClB,OAAO,OAAO,QAAY,KAC1B,OAAOE,EAAY,KACnB,OAAO,UAAc,IAGVC,EAAcH,GAAW,CAACC,EAG1BG,GAAS,OAAO,WAAW,IAAQ,IAGnCC,GAAU,OAAO,KAAS,IAG1BC,EAAa,CAACN,GAAW,CAACK,GAG1BE,EACXD,GACA,OAAO,QAAW,UAClB,OAAO,UAAa,UACpB,OAAO,SAAS,eAAkB,YAClC,mBAAoB,QACpB,OAAO,eAAkB,WAGdE,EACXF,GAAc,OAAO,eAAkB,YAAc,OAAO,MAAS,SAG1DG,GACX,OAAO,WAAc,UACrB,OAAO,UAAU,WAAc,UAC/B,UAAU,UAAU,QAAQ,QAAQ,GAAK,IACzC,UAAU,UAAU,QAAQ,QAAQ,EAAI,GCrC1C,IAAIC,EACAC,EACAC,EAEOC,EAEAC,EAaX,eAAsBC,GAAkB,CAmBtC,GAlBI,CAACC,IAILN,GAAc,KAAM,QAAO,UAAU,GAAG,QACxCG,EAAY,KAAM,QAAO,SAAS,EAClCC,EAAoB,KAAM,QAAO,kBAAkB,EAGnDF,GAAa,KAAM,QAAO,SAAS,GAAG,QACtCD,EAAW,KAAM,QAAO,WAAW,EACnCM,EAAUN,EAAS,IAOf,OAAOO,EAAY,KACrB,OAKF,IAAMC,EAAKN,EACLO,EAAS,KAAM,QAAO,aAAa,EACnCC,EAAK,KAAM,QAAO,IAAI,EACtBC,EAAgB,KAAM,QAAO,oBAAoB,EACjDC,EAAwC,CAC5C,GAAAJ,EACA,OAAAC,EACA,GAAAC,EACA,cAAAC,CACF,EAGC,WAAmB,QAAU,SAAUE,EAAkB,CACxD,OAAOD,EAAaC,CAAG,CACzB,CACF,CAxCsBC,EAAAV,EAAA,mBA0CtB,SAASW,GAAiBC,EAAcC,EAAuB,CAC7D,OAAOjB,EAAS,QAAQiB,GAAQ,IAAKD,CAAI,CAC3C,CAFSF,EAAAC,GAAA,oBAIT,SAASG,GAAoBF,EAAcC,EAAuB,CAChE,OAAIA,IAAS,SAEXA,EAAO,UAEF,IAAI,IAAID,EAAMC,CAAI,EAAE,SAAS,CACtC,CANSH,EAAAI,GAAA,uBAQF,IAAIC,EACPd,EACFc,EAAcJ,GAEdI,EAAcD,GAQT,IAAIZ,EAEND,IACHC,EAAU,KAYZ,SAASc,GACPJ,EACAK,EAGkC,CAKlC,OAJIL,EAAK,WAAW,SAAS,IAE3BA,EAAOA,EAAK,MAAM,CAAgB,GAEhCA,EAAK,SAAS,KAAK,EAEd,CAAE,SAAU,MAAMA,CAAI,CAAE,EAGxB,CACL,OAAQb,EACL,SAASa,CAAI,EACb,KACEM,GACC,IAAI,WAAWA,EAAK,OAAQA,EAAK,WAAYA,EAAK,UAAU,CAChE,CACJ,CAEJ,CAxBSR,EAAAM,GAAA,0BAmCT,SAASG,GACPP,EACAQ,EACqD,CACrD,IAAMC,EAAM,IAAI,IAAIT,EAAM,QAA0B,EAEpD,MAAO,CAAE,SAAU,MAAMS,EADXD,EAAkB,CAAE,UAAWA,CAAgB,EAAI,CAAC,CAC7B,CAAE,CACzC,CAPSV,EAAAS,GAAA,6BAUF,IAAIG,EAMPrB,EACFqB,EAAoBN,GAEpBM,EAAoBH,GAGtB,eAAsBI,EACpBX,EACAY,EACqB,CACrB,GAAM,CAAE,SAAAC,EAAU,OAAAC,CAAO,EAAIJ,EAAkBV,EAAMY,CAAsB,EAC3E,GAAIE,EACF,OAAOA,EAET,IAAMC,EAAI,MAAMF,EAChB,GAAI,CAACE,EAAE,GACL,MAAM,IAAI,MAAM,mBAAmBf,qBAAwB,EAE7D,OAAO,IAAI,WAAW,MAAMe,EAAE,YAAY,CAAC,CAC7C,CAbsBjB,EAAAa,EAAA,kBAoBf,IAAIK,EAEX,GAAIC,EAEFD,EAAalB,EAAA,MAAOW,GAAQ,MAAM,OAAiCA,GAAtD,sBACJS,EAETF,EAAalB,EAAA,MAAOW,GAAQ,CAC1B,GAAI,CAEF,WAAW,cAAcA,CAAG,CAC9B,OAASU,EAAP,CAEA,GAAIA,aAAa,UACf,MAAM,OAAiCV,OAEvC,OAAMU,CAEV,CACF,EAZa,sBAaJ9B,EACT2B,EAAaI,OAEb,OAAM,IAAI,MAAM,sCAAsC,EAQxD,eAAeA,GAAeX,EAAa,CACrCA,EAAI,WAAW,SAAS,IAE1BA,EAAMA,EAAI,MAAM,CAAgB,GAE9BA,EAAI,SAAS,KAAK,EAEpBxB,EAAU,iBAAiB,MAAO,MAAM,MAAMwB,CAAG,GAAG,KAAK,CAAC,EAI1D,MAAM,OAAiC1B,EAAW,cAAc0B,CAAG,EAAE,KAEzE,CAbeX,EAAAsB,GAAA,kBAef,eAAsBC,EAAaC,EAAwC,CACzE,GAAIjC,EAAS,CACX,MAAMD,EAAgB,EACtB,IAAMmC,EAAiB,MAAMpC,EAAkB,SAASmC,EAAa,CACnE,SAAU,MACZ,CAAC,EACD,OAAO,KAAK,MAAMC,CAAc,MAGhC,QAAO,MADQ,MAAM,MAAMD,CAAW,GAChB,KAAK,CAE/B,CAXsBxB,EAAAuB,EAAA,gBAiBtB,eAAsBG,GAAoC,CACxD,GAAIC,EACF,OAAO,UAGT,IAAIC,EACJ,GAAI,CACF,MAAM,IAAI,KACZ,OAASP,EAAP,CACAO,EAAMP,CACR,CACA,IAAIQ,EAAWC,EAAiB,MAAMF,CAAG,EAAE,CAAC,EAAE,SAM9C,GAJIrC,GAAW,CAACsC,EAAS,WAAW,SAAS,IAC3CA,EAAW,UAAUA,KAGnBE,EAAa,CACf,IAAM7C,EAAW,KAAM,QAAO,WAAW,EAKzC,OAJgB,KAAM,QAAO,UAAU,GAIxB,cAAcA,EAAS,QAAQ2C,CAAQ,CAAC,EAGzD,IAAMG,EAAmBH,EAAS,YAAYrC,CAAO,EACrD,GAAIwC,IAAqB,GACvB,MAAM,IAAI,MACR,8DACF,EAEF,OAAOH,EAAS,MAAM,EAAGG,CAAgB,CAC3C,CAjCsBhC,EAAA0B,EAAA,oBCxNf,SAASO,EAAmBC,EAAgB,CACjD,IAAMC,EAAKD,EAAO,GACZE,EAAQF,EAAO,GAAG,YAAY,MAC9BG,EAAOH,EAAO,KAEdI,EAAgB,CAGpB,SAAU,MACV,UAAW,MACX,MAAO,SAAUC,EAAY,CAC3B,GAAI,CAACA,EAAM,KAAK,iBACd,MAAM,IAAI,MAAM,mCAAmC,EAIrD,OAAOH,EAAM,MAAM,MAAM,KAAM,SAAS,CAC1C,EACA,OAAQ,MAAOG,EAAYC,EAAmBC,IAAuB,CACnE,GAAI,CACF,IAAMC,EAAQJ,EAAc,YAAYC,CAAK,EACvCI,EAAS,MAAML,EAAc,aAAaC,CAAK,EAC/CK,EAAMJ,EAAWG,EAASD,EAC1BG,EAAML,EAAWE,EAAQC,EAC/B,MAAML,EAAc,UAAUC,EAAOK,EAAKC,CAAG,EAC7CJ,EAAS,IAAI,CACf,OAASK,EAAP,CACAL,EAASK,CAAC,CACZ,CACF,EAEA,YAAcP,GAAe,CAC3B,IAAIQ,EAAU,OAAO,OAAO,IAAI,EAEhC,SAASC,EAAUC,EAAW,CAC5B,OAAOA,IAAM,KAAOA,IAAM,IAC5B,CAFSC,EAAAF,EAAA,aAIT,SAASG,EAAWC,EAAc,CAChC,OAAQH,GACCZ,EAAK,MAAMe,EAAMH,CAAC,CAE7B,CAJSC,EAAAC,EAAA,cAMT,IAAIE,EAAQlB,EAAG,QAAQI,EAAM,UAAU,EACpC,OAAOS,CAAS,EAChB,IAAIG,EAAWZ,EAAM,UAAU,CAAC,EAEnC,KAAOc,EAAM,QAAQ,CACnB,IAAIC,EAAOD,EAAM,IAAI,EACjBE,EAAOpB,EAAG,KAAKmB,CAAI,EAEnBnB,EAAG,MAAMoB,EAAK,IAAI,GACpBF,EAAM,KAAK,MACTA,EACAlB,EAAG,QAAQmB,CAAI,EAAE,OAAON,CAAS,EAAE,IAAIG,EAAWG,CAAI,CAAC,CACzD,EAGFP,EAAQO,CAAI,EAAI,CAAE,UAAWC,EAAK,MAAO,KAAMA,EAAK,IAAK,EAG3D,MAAO,CAAE,KAAM,QAAS,QAASR,CAAQ,CAC3C,EAEA,aAAc,MAAOR,GAAe,CAElC,IAAMQ,EAAU,OAAO,OAAO,IAAI,EAE5BS,EAAU,MAAMC,GAAalB,EAAM,KAAK,gBAAgB,EAC9D,OAAW,CAACe,EAAMI,CAAM,IAAKF,EACvBF,IAAS,MAEbP,EAAQV,EAAK,MAAME,EAAM,WAAYe,CAAI,CAAC,EAAI,CAC5C,UACEI,EAAO,OAAS,QACX,MAAMA,EAAO,QAAQ,GAAG,iBACzB,IAAI,KACV,KACEA,EAAO,OAAS,OACZpB,EAAc,UACdA,EAAc,QACtB,GAGF,MAAO,CAAE,KAAM,SAAU,QAAAS,EAAS,QAAAS,CAAQ,CAC5C,EACA,eAAiBF,GAAiB,CAEhC,IAAMK,EADSxB,EAAG,WAAWmB,CAAI,EACb,KACdC,EAAOpB,EAAG,KAAKmB,CAAI,EAEzB,GAAInB,EAAG,MAAMoB,EAAK,IAAI,EACpB,MAAO,CAAE,UAAWA,EAAK,MAAO,KAAMA,EAAK,IAAK,EAC3C,GAAIpB,EAAG,OAAOoB,EAAK,IAAI,EAC5B,OAAAI,EAAK,SAAWvB,EAAM,wBAAwBuB,CAAI,EAC3C,CACL,UAAWJ,EAAK,MAChB,KAAMA,EAAK,KACX,SAAUI,EAAK,QACjB,EAEA,MAAM,IAAI,MAAM,yBAAyB,CAE7C,EACA,gBAAiB,CAACL,EAAcM,IAAe,CAC7C,GAAIzB,EAAG,MAAMyB,EAAM,IAAO,EACxBzB,EAAG,UAAUmB,EAAMM,EAAM,IAAO,UACvBzB,EAAG,OAAOyB,EAAM,IAAO,EAChCzB,EAAG,UAAUmB,EAAMM,EAAM,SAAa,CAAE,OAAQ,EAAK,CAAC,MAEtD,OAAM,IAAI,MAAM,yBAAyB,EAG3CzB,EAAG,MAAMmB,EAAMM,EAAM,IAAO,EAC5BzB,EAAG,MAAMmB,EAAMM,EAAM,UAAcA,EAAM,SAAY,CACvD,EACA,iBAAmBN,GAAiB,CAClC,IAAIC,EAAOpB,EAAG,KAAKmB,CAAI,EAEnBnB,EAAG,MAAMoB,EAAK,IAAI,EACpBpB,EAAG,MAAMmB,CAAI,EACJnB,EAAG,OAAOoB,EAAK,IAAI,GAC5BpB,EAAG,OAAOmB,CAAI,CAElB,EACA,gBAAiB,MAAOI,GAAgB,CACtC,GAAIA,EAAO,OAAS,OAAQ,CAC1B,IAAMG,EAAO,MAAMH,EAAO,QAAQ,EAClC,MAAO,CACL,SAAU,IAAI,WAAW,MAAMG,EAAK,YAAY,CAAC,EACjD,KAAMvB,EAAc,UACpB,UAAWuB,EAAK,gBAClB,MACK,IAAIH,EAAO,OAAS,YACzB,MAAO,CACL,KAAMpB,EAAc,SACpB,UAAW,IAAI,IACjB,EAEA,MAAM,IAAI,MAAM,iBAAmBoB,EAAO,IAAI,EAElD,EACA,iBAAkB,MAAOF,EAAcF,EAAcM,IAAe,CAClE,IAAME,EAAkBN,EAAQ,IAAInB,EAAK,QAAQiB,CAAI,CAAC,EAChDI,EAASvB,EAAG,OAAOyB,EAAM,IAAI,EAC/B,MAAME,EAAgB,cAAczB,EAAK,SAASiB,CAAI,EAAG,CACvD,OAAQ,EACV,CAAC,EACD,MAAMQ,EAAgB,mBAAmBzB,EAAK,SAASiB,CAAI,EAAG,CAC5D,OAAQ,EACV,CAAC,EACL,GAAII,EAAO,OAAS,OAAQ,CAC1B,IAAMK,EAAW,MAAML,EAAO,eAAe,EAC7C,MAAMK,EAAS,MAAMH,EAAM,QAAQ,EACnC,MAAMG,EAAS,MAAM,EAEvBP,EAAQ,IAAIF,EAAMI,CAAM,CAC1B,EACA,kBAAmB,MAAOF,EAAcF,IAAiB,CAEvD,MADwBE,EAAQ,IAAInB,EAAK,QAAQiB,CAAI,CAAC,EAChC,YAAYjB,EAAK,SAASiB,CAAI,CAAC,EACrDE,EAAQ,OAAOF,CAAI,CACrB,EACA,UAAW,MAAOf,EAAYK,EAAUC,IAAa,CACnD,IAAImB,EAAQ,EAENC,EAAwB,CAAC,EAC/B,OAAO,KAAKrB,EAAI,OAAO,EAAE,QAAQ,SAAUsB,EAAK,CAC9C,IAAMpB,EAAIF,EAAI,QAAQsB,CAAG,EACnBC,EAAKtB,EAAI,QAAQqB,CAAG,GAExB,CAACC,GACAhC,EAAG,OAAOW,EAAE,IAAI,GACfA,EAAE,UAAa,QAAQ,EAAIqB,EAAG,UAAa,QAAQ,KAErDF,EAAO,KAAKC,CAAG,EACfF,IAEJ,CAAC,EAGDC,EAAO,KAAK,EAEZ,IAAMG,EAAwB,CAAC,EAW/B,GAVA,OAAO,KAAKvB,EAAI,OAAO,EAAE,QAAQ,SAAUqB,EAAK,CACzCtB,EAAI,QAAQsB,CAAG,IAClBE,EAAO,KAAKF,CAAG,EACfF,IAEJ,CAAC,EAGDI,EAAO,KAAK,EAAE,QAAQ,EAElB,CAACJ,EACH,OAGF,IAAMR,EAAUZ,EAAI,OAAS,SAAWA,EAAI,QAAUC,EAAI,QAE1D,QAAWS,KAAQW,EAAQ,CACzB,IAAMI,EAAUhC,EAAK,UACnBiB,EAAK,QAAQf,EAAM,WAAY,GAAG,CACpC,EAAE,UAAU,CAAC,EACb,GAAIM,EAAI,OAAS,QAAS,CACxB,IAAMa,EAASF,EAAQ,IAAIa,CAAO,EAC5BT,GAAQ,MAAMtB,EAAc,gBAAgBoB,CAAM,EACxDpB,EAAc,gBAAgBgB,EAAMM,EAAK,MACpC,CACL,IAAMA,EAAQtB,EAAc,eAAegB,CAAI,EAC/C,MAAMhB,EAAc,iBAAiBkB,EAASa,EAAST,CAAK,GAIhE,QAAWN,KAAQc,EACjB,GAAIvB,EAAI,OAAS,QACfP,EAAc,iBAAiBgB,CAAI,MAC9B,CACL,IAAMe,EAAUhC,EAAK,UACnBiB,EAAK,QAAQf,EAAM,WAAY,GAAG,CACpC,EAAE,UAAU,CAAC,EACb,MAAMD,EAAc,kBAAkBkB,EAASa,CAAO,EAG5D,CACF,EAEAnC,EAAO,GAAG,YAAY,eAAiBI,CACzC,CArOgBY,EAAAjB,EAAA,sBAuOhB,IAAMwB,GAAeP,EAAA,MAAOoB,GAAmB,CAC7C,IAAMd,EAAe,CAAC,EAEtB,eAAee,EAAQC,EAAmB,CACxC,cAAiBZ,KAASY,EAAa,OAAO,EAC5ChB,EAAQ,KAAKI,CAAK,EACdA,EAAM,OAAS,aACjB,MAAMW,EAAQX,CAAK,CAGzB,CAPeV,EAAAqB,EAAA,WASf,MAAMA,EAAQD,CAAS,EAEvB,IAAMG,EAAS,IAAI,IACnBA,EAAO,IAAI,IAAKH,CAAS,EACzB,QAAWZ,KAAUF,EAAS,CAC5B,IAAMkB,GAAgB,MAAMJ,EAAU,QAAQZ,CAAM,GAAG,KAAK,GAAG,EAC/De,EAAO,IAAIC,EAAchB,CAAM,EAEjC,OAAOe,CACT,EArBqB,gBCjOd,SAASE,GAAeC,EAAwC,CACrE,IAAMC,EAA+B,CACnC,gBAAiB,GACjB,gBAAiB,GACjB,eAAgB,GAChB,OAAQC,GAAiCF,CAAM,EAC/C,KAAKG,EAAgBC,EAAgB,CAGnC,MAAAH,EAAS,OAAS,CAAE,OAAAE,EAAQ,QAAAC,CAAQ,EAC9BA,CACR,EACA,MAAOJ,EAAO,OACd,SAAUA,EAAO,OACjB,UAAWA,EAAO,KAClB,IAAK,CAAE,OAAAA,CAAO,EAYd,WAAaK,GAAiBL,EAAO,SAAWK,EAChD,gBAAiBC,GAAuBN,EAAO,QAAQ,CACzD,EACA,OAAOC,CACT,CA/BgBM,EAAAR,GAAA,kBAyChB,SAASS,GAAoBH,EAA0B,CACrD,OAAO,SAAUI,EAAQ,CACvB,IAAMC,EAAe,IACrB,GAAI,CACFD,EAAO,GAAG,UAAUJ,CAAI,CAC1B,OAASM,EAAP,CACA,QAAQ,MAAM,iDAAiDN,KAAQ,EACvE,QAAQ,MAAMM,CAAC,EACf,QAAQ,MAAM,UAAUD,iCAA4C,EACpEL,EAAOK,CACT,CACAD,EAAO,GAAG,MAAMJ,CAAI,CACtB,CACF,CAbSE,EAAAC,GAAA,uBAeT,SAASI,GAAeC,EAA4C,CAClE,OAAO,SAAUJ,EAAQ,CACvB,OAAO,OAAOA,EAAO,IAAKI,CAAG,CAC/B,CACF,CAJSN,EAAAK,GAAA,kBAUT,SAASE,GAAsBC,EAA8B,CAC3D,OAAQN,GAAW,CACjB,QAAWO,KAASD,EAClBN,EAAO,GAAG,UAAUO,CAAK,EACzBP,EAAO,GAAG,MAAMA,EAAO,GAAG,YAAY,OAAQ,CAAE,KAAMO,CAAM,EAAGA,CAAK,CAExE,CACF,CAPST,EAAAO,GAAA,yBAsBT,SAASG,GAAcC,EAA+B,CACpD,IAAMC,EAAqCC,EAAeF,CAAS,EACnE,OAAQT,GAAW,CAEjB,IAAMY,EAAUZ,EAAO,kBAAkB,EAEnCa,EAAUb,EAAO,kBAAkB,EAEzCA,EAAO,GAAG,UAAU,MAAM,EAC1BA,EAAO,GAAG,UAAU,cAAcY,KAAWC,iBAAuB,EAEpEb,EAAO,iBAAiB,gBAAgB,EAExCU,EACG,KAAMI,GAAuB,CAC5Bd,EAAO,GAAG,UAAU,cAAcY,IAAUC,QAAeC,CAAM,CACnE,CAAC,EACA,MAAOZ,GAAM,CACZ,QAAQ,MAAM,uDAAuD,EACrE,QAAQ,MAAMA,CAAC,CACjB,CAAC,EACA,QAAQ,IAAM,CACbF,EAAO,oBAAoB,gBAAgB,CAC7C,CAAC,CACL,CACF,CAzBSF,EAAAU,GAAA,iBA+BT,SAASf,GAAiCF,EAAkC,CAC1E,IAAIwB,EACJ,OAAIxB,EAAO,WAAa,KACtBwB,EAAYxB,EAAO,UAEnBwB,EAAYxB,EAAO,SAAW,oBAGzB,CACLiB,GAAcO,CAAS,EACvBhB,GAAoBR,EAAO,IAAI,IAAI,EACnCY,GAAeZ,EAAO,GAAG,EACzBc,GAAsBd,EAAO,YAAY,EACzCyB,CACF,CACF,CAfSlB,EAAAL,GAAA,oCAiBT,SAASI,GACPoB,EACuC,CAEvC,GAAiB,OAAO,oBAAwB,IAQ9C,OAEF,GAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAS,EAAIC,EAAkBH,EAAW,kBAAkB,EAC5E,OAAO,SACLI,EACAC,EAIA,CACA,OAAC,gBAAkB,CACjB,GAAI,CACF,IAAIC,EACAJ,EACFI,EAAM,MAAM,YAAY,qBAAqBJ,EAAUE,CAAO,EAE9DE,EAAM,MAAM,YAAY,YAAY,MAAML,EAAQG,CAAO,EAE3D,GAAM,CAAE,SAAAG,EAAU,OAAAC,CAAO,EAAIF,EAC7BD,EAAgBE,EAAUC,CAAM,CAClC,OAASvB,EAAP,CACA,QAAQ,KAAK,4BAA4B,EACzC,QAAQ,KAAKA,CAAC,CAChB,CACF,EAAG,EAEI,CAAC,CACV,CACF,CAxCSJ,EAAAD,GAAA,0BCxKF,IAAM6B,EAAkB,SCgE/B,eAAsBC,EACpBC,EA+HI,CAAC,EACsB,CAzM7B,IAAAC,EAAAC,EA0ME,MAAMC,EAAgB,EACtB,IAAIC,EAAWJ,EAAQ,UAAa,MAAMK,EAAiB,EAC3DD,EAAWE,EAAYF,CAAQ,EAC1BA,EAAS,SAAS,GAAG,IACxBA,GAAY,KAEdJ,EAAQ,SAAWI,EAEnB,IAAMG,EAAiB,CACrB,WAAY,GACZ,UAAW,WACX,MAAO,WAAW,OAAS,WAAW,OAAS,OAC/C,YAAaH,EAAW,oBACxB,KAAM,CAAC,EACP,aAAc,CAAC,EACf,IAAK,CAAC,EACN,gBAAiBA,EACjB,SAAU,CAAC,EACX,uBAAwB,GACxB,gBAAiB,GACjB,2EACF,EACMI,EAAS,OAAO,OAAOD,EAAgBP,CAAO,GACpDC,EAAAO,EAAO,KAAI,OAAXP,EAAW,KAAS,kBASpBC,EAAAM,EAAO,KAAI,gBAAXN,EAAW,cAAkB,KAC7B,IAAMO,EAAqBC,GAAeF,CAAM,EAC1CG,EAAMF,EAAmB,IAM/B,GALAE,EAAI,gBAAkBC,EAAaJ,EAAO,WAAW,EAKjD,OAAO,sBAAyB,WAAY,CAC9C,IAAMK,EAAY,GAAGL,EAAO,yBAC5B,MAAMM,EAAWD,CAAS,EAG5B,IAAIE,EACJ,GAAIf,EAAQ,cAAe,CACzB,IAAMgB,EAAM,MAAMhB,EAAQ,cACtB,YAAY,OAAOgB,CAAG,EACxBD,EAAWC,EAEXD,EAAW,IAAI,WAAWC,CAAG,EAE/BP,EAAmB,aAAe,GAElCA,EAAmB,eAAiBM,EAAS,OAK/C,IAAME,EAAS,MAAM,qBAAqBR,CAAkB,EAE5D,GAAIA,EAAmB,OACrB,MAAMA,EAAmB,OAAO,QAMlC,GAJIT,EAAQ,qBACVW,EAAI,yBAAyB,EAAI,EAG/BA,EAAI,UAAYO,GAAWV,EAAO,gBACpC,MAAM,IAAI,MAAM,oCACeU,YAAkBP,EAAI,0HAExD,EAGCM,EAAO,WAAcE,GAAiB,CACpC,MAAM,IAAI,MAAM,qDAAqD,CACvE,EAEA,IAAIC,EACAL,IACFK,EAAiBT,EAAI,gBAAgBI,CAAQ,GAG/C,IAAMM,EAAUV,EAAI,kBAClBS,EACApB,EAAQ,qBACV,EACA,OAAAW,EAAI,IAAI,KAAK,OAAO,EAAGA,EAAI,OAAO,IAAI,IAAI,EAErCU,EAAQ,QAAQ,SAAS,KAAK,GAGjCV,EAAI,UAAU,qCAAqCU,EAAQ,eAAe,EAE5EV,EAAI,SAAS,eAAe,EAC5B,MAAMA,EAAI,kBAGVA,EAAI,kBAAkBH,EAAO,MAAOA,EAAO,OAAQA,EAAO,MAAM,EACzDa,CACT,CAxOsBC,EAAAvB,EAAA,eRrErB,WAAmB,YAAcwB", "names": ["pyodide_umd_exports", "__export", "loadPyodide", "version", "_isNumber", "n", "__name", "_capitalize", "str", "_getter", "p", "booleanProps", "numericProps", "stringProps", "arrayProps", "objectProps", "props", "StackFrame", "obj", "i", "v", "fileName", "lineNumber", "columnNumber", "functionName", "argsStartIndex", "argsEndIndex", "args", "locationString", "parts", "j", "k", "stackframe_default", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CHROME_IE_STACK_REGEXP", "SAFARI_NATIVE_CODE_REGEXP", "__name", "error", "urlLike", "regExp", "parts", "filtered", "line", "sanitizedLine", "location", "locationParts", "functionName", "fileName", "stackframe_default", "functionNameRegex", "matches", "error<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error_stack_parser_default", "error<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IN_NODE", "IN_NODE_COMMONJS", "__require", "IN_NODE_ESM", "IN_BUN", "IN_DENO", "IN_BROWSER", "IN_BROWSER_MAIN_THREAD", "IN_BROWSER_WEB_WORKER", "IN_SAFARI", "nodeUrlMod", "nodePath", "nodeVmMod", "nodeFSMod", "nodeFsPromisesMod", "initNodeModules", "IN_NODE", "pathSep", "__require", "fs", "crypto", "ws", "child_process", "node_modules", "mod", "__name", "node_resolvePath", "path", "base", "browser_resolvePath", "<PERSON><PERSON><PERSON>", "node_getBinaryResponse", "_file_sub_resource_hash", "data", "browser_getBinaryResponse", "subResourceHash", "url", "getBinaryResponse", "loadBinaryFile", "file_sub_resource_hash", "response", "binary", "r", "loadScript", "IN_BROWSER_MAIN_THREAD", "IN_BROWSER_WEB_WORKER", "e", "nodeLoadScript", "loadLockFile", "lockFileURL", "package_string", "calculateDirname", "IN_NODE_COMMONJS", "err", "fileName", "error_stack_parser_default", "IN_NODE_ESM", "indexOfLastSlash", "initializeNativeFS", "module", "FS", "MEMFS", "PATH", "nativeFSAsync", "mount", "populate", "callback", "local", "remote", "src", "dst", "e", "entries", "isRealDir", "p", "__name", "toAbsolute", "root", "check", "path", "stat", "handles", "get<PERSON>sHandles", "handle", "node", "entry", "file", "parentDirHandle", "writable", "total", "create", "key", "e2", "remove", "re<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "collect", "cur<PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "relativePath", "createSettings", "config", "settings", "getFileSystemInitializationFuncs", "status", "toThrow", "path", "getInstantiateWasmFunc", "__name", "createHomeDirectory", "<PERSON><PERSON><PERSON>", "fallback<PERSON><PERSON>", "e", "setEnvironment", "env", "mountLocalDirectories", "mounts", "mount", "installStdlib", "stdlibURL", "stdlibPromise", "loadBinaryFile", "<PERSON><PERSON><PERSON><PERSON>", "pyminor", "stdlib", "stdLibURL", "initializeNativeFS", "indexURL", "binary", "response", "getBinaryResponse", "imports", "success<PERSON>allback", "res", "instance", "module", "version", "loadPyodide", "options", "_a", "_b", "initNodeModules", "indexURL", "calculateDirname", "<PERSON><PERSON><PERSON>", "default_config", "config", "emscriptenSettings", "createSettings", "API", "loadLockFile", "scriptSrc", "loadScript", "snapshot", "snp", "<PERSON><PERSON><PERSON>", "version", "path", "snapshotConfig", "pyodide", "__name", "loadPyodide"]}
.rose-pine * {
	color: #e0def4 !important;
	stroke: #907aa9 !important;
}

.rose-pine .app > * {
	background-color: #1f1d2e !important;
}

.rose-pine #nav {
	background-color: #191724;
}

.rose-pine .py-2\.5.my-auto.flex.flex-col.justify-between.h-screen {
	background: #191724;
}

.rose-pine .bg-white.dark\:bg-gray-800 {
	background: #26233a;
}

.rose-pine .w-4.h-4 {
	fill: #c4a7e7;
}

.rose-pine #chat-input {
	background: #393552;
	margin: 0.3rem;
	padding: 0.5rem;
}

.rose-pine .bg-gradient-to-t.from-white.dark\:from-gray-800.from-40\%.pb-2 {
	background: #26233a !important;
	padding-top: 0.6rem;
}

.rose-pine
	.text-white.bg-gray-100.dark\:text-gray-800.dark\:bg-gray-600.disabled.transition.rounded-lg.p-1.mr-0\.5.w-7.h-7.self-center {
	background-color: #6e6a86;
	transition: background-color 0.2s ease-out linear;
}

.rose-pine
	.bg-black.text-white.hover\:bg-gray-900.dark\:bg-white.dark\:text-black.dark\:hover\:bg-gray-100.transition.rounded-lg.p-1.mr-0\.5.w-7.h-7.self-center {
	background-color: #286983;
	transition: background-color 0.2s ease-out linear;
}

.rose-pine
	.bg-black.text-white.hover\:bg-gray-900.dark\:bg-white.dark\:text-black.dark\:hover\:bg-gray-100.transition.rounded-lg.p-1.mr-0\.5.w-7.h-7.self-center
	> * {
	fill: #9ccfd8 !important;
	transition: fill 0.2s ease-out linear;
}

.rose-pine
	.w-full.flex.justify-between.rounded-md.px-3.py-2.hover\:bg-gray-900.bg-gray-900.transition.whitespace-nowrap.text-ellipsis {
	background-color: #56526e;
	font-weight: bold;
}

.rose-pine .hover\:bg-gray-900:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(57 53 82 / var(--tw-bg-opacity));
}

.rose-pine .text-xs.text-gray-700.uppercase.bg-gray-50.dark\:bg-gray-700.dark\:text-gray-400 {
	background-color: #403d52;
}

.rose-pine .scrollbar-hidden.relative.overflow-x-auto.whitespace-nowrap.svelte-3g4avz {
	border-radius: 16px 16px 0 0;
}

.rose-pine .base.enter.svelte-ug60r4 {
	background-color: #393552;
}

.rose-pine .message.svelte-1nauejd {
	color: #e0def4 !important;
}

.rose-pine #dropdownDots {
	background-color: #403d52;
}

.rose-pine .flex.py-2\.5.px-3\.5.w-full.hover\:bg-gray-800.transition:hover {
	background: #524f67;
}

.rose-pine .m-auto.rounded-xl.max-w-full.w-\[40rem\].mx-2.bg-gray-50.dark\:bg-gray-900.shadow-3xl {
	background-color: #26233a;
}

.rose-pine
	.w-full.rounded.p-4.text-sm.dark\:text-gray-300.dark\:bg-gray-800.outline-none.resize-none {
	background-color: #524f67;
}

.rose-pine
	.w-full.rounded.py-2.px-4.text-sm.dark\:text-gray-300.dark\:bg-gray-800.outline-none.svelte-1vx7r9s {
	background-color: #524f67;
}

.rose-pine
	.px-2\.5.py-2\.5.min-w-fit.rounded-lg.flex-1.md\:flex-none.flex.text-right.transition.bg-gray-200.dark\:bg-gray-700 {
	background-color: #403d52;
}

.rose-pine
	.px-2\.5.py-2\.5.min-w-fit.rounded-lg.flex-1.md\:flex-none.flex.text-right.transition.hover\:bg-gray-300.dark\:hover\:bg-gray-800:hover {
	background-color: #524f67;
}

.rose-pine .px-4.py-2.bg-emerald-600.hover\:bg-emerald-700.text-gray-100.transition.rounded {
	background-color: #31748f;
}

.rose-pine #chat-search > * {
	background-color: #403d52 !important;
}

.rose-pine .svelte-1ee93ns {
	--primary: #eb6f92 !important;
	--secondary: #e0def4 !important;
}

.rose-pine .svelte-11kvm4p {
	--primary: #9ccfd8 !important;
	--secondary: #1f1d2e !important;
}

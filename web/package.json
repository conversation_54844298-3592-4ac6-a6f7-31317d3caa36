{"name": "dify-web", "version": "0.15.6-alpha.1", "private": true, "engines": {"node": ">=18.17.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "cp -r .next/static .next/standalone/.next/static && cp -r public .next/standalone/public && cross-env PORT=$npm_config_port HOSTNAME=$npm_config_host node .next/standalone/server.js", "lint": "next lint", "fix": "next lint --fix", "eslint-fix": "eslint --fix", "prepare": "cd ../ && node -e \"if (process.env.NODE_ENV !== 'production'){process.exit(1)} \" || husky install ./web/.husky", "gen-icons": "node ./app/components/base/icons/script.js", "uglify-embed": "node ./bin/uglify-embed", "check-i18n": "node ./i18n/check-i18n.js", "auto-gen-i18n": "node ./i18n/auto-gen-i18n.js", "test": "jest", "test:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@babel/runtime": "^7.22.3", "@dagrejs/dagre": "^1.1.2", "@emoji-mart/data": "^1.1.2", "@floating-ui/react": "^0.25.2", "@formatjs/intl-localematcher": "^0.5.4", "@headlessui/react": "^1.7.13", "@heroicons/react": "^2.0.16", "@hookform/resolvers": "^3.3.4", "@lexical/react": "^0.16.0", "@mdx-js/loader": "^2.3.0", "@mdx-js/react": "^2.3.0", "@monaco-editor/react": "^4.6.0", "@next/mdx": "^14.0.4", "@remixicon/react": "^4.5.0", "@sentry/react": "^7.54.0", "@sentry/utils": "^7.54.0", "@svgdotjs/svg.js": "^3.2.4", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.9", "@tanstack/react-query": "^5.60.5", "@tanstack/react-query-devtools": "^5.60.5", "ahooks": "^3.7.5", "class-variance-authority": "^0.7.0", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "decimal.js": "^10.4.3", "dompurify": "^3.2.4", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "elkjs": "^0.9.3", "emoji-mart": "^5.5.2", "fast-deep-equal": "^3.1.3", "i18next": "^22.4.13", "i18next-resources-to-backend": "^1.1.3", "immer": "^9.0.19", "js-audio-recorder": "^1.0.7", "js-cookie": "^3.0.1", "jwt-decode": "^4.0.0", "katex": "^0.16.21", "lamejs": "^1.2.1", "lexical": "^0.16.0", "line-clamp": "^1.0.0", "lodash-es": "^4.17.21", "mermaid": "11.4.1", "mime": "^4.0.4", "negotiator": "^0.6.3", "next": "^14.2.25", "pinyin-pro": "^3.23.0", "qrcode.react": "^3.1.0", "qs": "^6.11.1", "rc-textarea": "^1.5.2", "react": "~18.2.0", "react-18-input-autosize": "^3.0.0", "react-dom": "~18.2.0", "react-easy-crop": "^5.0.8", "react-error-boundary": "^4.0.2", "react-hook-form": "^7.51.4", "react-hotkeys-hook": "^4.6.1", "react-i18next": "^12.2.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^8.0.6", "react-multi-email": "^1.0.14", "react-papaparse": "^4.1.0", "react-pdf-highlighter": "^8.0.0-rc.0", "react-slider": "^2.0.4", "react-sortablejs": "^6.1.4", "react-syntax-highlighter": "^15.5.0", "react-tooltip": "5.8.3", "react-window": "^1.8.9", "react-window-infinite-loader": "^1.0.9", "reactflow": "^11.11.3", "recordrtc": "^5.6.2", "rehype-katex": "^6.0.2", "rehype-raw": "^7.0.0", "remark-breaks": "^3.0.2", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "scheduler": "^0.23.0", "server-only": "^0.0.1", "sharp": "^0.33.2", "shave": "^5.0.4", "sortablejs": "^1.15.0", "swr": "^2.1.0", "tailwind-merge": "^2.4.0", "use-context-selector": "^1.4.1", "uuid": "^9.0.1", "zod": "^3.23.6", "zundo": "^2.1.0", "zustand": "^4.5.2"}, "devDependencies": {"@antfu/eslint-config": "^0.36.0", "@chromatic-com/storybook": "^1.9.0", "@faker-js/faker": "^7.6.0", "@rgrove/parse-xml": "^4.1.0", "@storybook/addon-essentials": "^8.3.5", "@storybook/addon-interactions": "^8.3.5", "@storybook/addon-links": "^8.3.5", "@storybook/addon-onboarding": "^8.3.5", "@storybook/addon-themes": "^8.3.5", "@storybook/blocks": "^8.3.5", "@storybook/nextjs": "^8.3.5", "@storybook/react": "^8.3.5", "@storybook/test": "^8.3.5", "@testing-library/dom": "^10.3.2", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@types/crypto-js": "^4.1.1", "@types/dagre": "^0.7.52", "@types/jest": "^29.5.12", "@types/js-cookie": "^3.0.3", "@types/lodash-es": "^4.17.7", "@types/negotiator": "^0.6.1", "@types/node": "18.15.0", "@types/qs": "^6.9.7", "@types/react": "~18.2.0", "@types/react-dom": "~18.2.0", "@types/react-slider": "^1.3.1", "@types/react-syntax-highlighter": "^15.5.6", "@types/react-window": "^1.8.5", "@types/react-window-infinite-loader": "^1.0.6", "@types/recordrtc": "^5.6.11", "@types/sortablejs": "^1.15.1", "@types/uuid": "^9.0.8", "autoprefixer": "^10.4.14", "bing-translate-api": "^4.0.2", "code-inspector-plugin": "^0.18.1", "cross-env": "^7.0.3", "eslint": "^8.36.0", "eslint-config-next": "^14.0.4", "eslint-plugin-storybook": "^0.9.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^13.2.2", "magicast": "^0.3.4", "postcss": "^8.4.31", "sass": "^1.61.0", "storybook": "^8.3.5", "tailwindcss": "^3.4.4", "ts-node": "^10.9.2", "typescript": "4.9.5", "uglify-js": "^3.17.4"}, "resolutions": {"@types/react": "~18.2.0", "@types/react-dom": "~18.2.0", "string-width": "4.2.3"}, "lint-staged": {"**/*.js?(x)": ["eslint --fix"], "**/*.ts?(x)": ["eslint --fix"]}}